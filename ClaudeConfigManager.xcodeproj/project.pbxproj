// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 56;
	objects = {

/* Begin PBXBuildFile section */
		A1234567890ABCDEF1234567 /* ClaudeConfigManagerApp.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1234567890ABCDEF1234566 /* ClaudeConfigManagerApp.swift */; };
		A1234567890ABCDEF1234568 /* ContentView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1234567890ABCDEF1234569 /* ContentView.swift */; };
		A1234567890ABCDEF123456A /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = A1234567890ABCDEF123456B /* Assets.xcassets */; };
		A1234567890ABCDEF123456C /* Preview Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = A1234567890ABCDEF123456D /* Preview Assets.xcassets */; };
		A1234567890ABCDEF123456E /* AppDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1234567890ABCDEF123456F /* AppDelegate.swift */; };
		A1234567890ABCDEF1234570 /* ClaudeConfig.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1234567890ABCDEF1234571 /* ClaudeConfig.swift */; };
		A1234567890ABCDEF1234572 /* ConfigService.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1234567890ABCDEF1234573 /* ConfigService.swift */; };
		A1234567890ABCDEF1234574 /* StatusItemManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1234567890ABCDEF1234575 /* StatusItemManager.swift */; };
		A1234567890ABCDEF1234576 /* MenuBarView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1234567890ABCDEF1234577 /* MenuBarView.swift */; };
		A1234567890ABCDEF1234578 /* MenuBarViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1234567890ABCDEF1234579 /* MenuBarViewModel.swift */; };
		A1234567890ABCDEF123457A /* AppState.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1234567890ABCDEF123457B /* AppState.swift */; };
		A1234567890ABCDEF123457C /* KeychainService.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1234567890ABCDEF123457D /* KeychainService.swift */; };
		A1234567890ABCDEF123457E /* ProcessService.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1234567890ABCDEF123457F /* ProcessService.swift */; };
		A1234567890ABCDEF1234580 /* Logger.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1234567890ABCDEF1234581 /* Logger.swift */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		A1234567890ABCDEF1234565 /* ClaudeConfigManager.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = ClaudeConfigManager.app; sourceTree = BUILT_PRODUCTS_DIR; };
		A1234567890ABCDEF1234566 /* ClaudeConfigManagerApp.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ClaudeConfigManagerApp.swift; sourceTree = "<group>"; };
		A1234567890ABCDEF1234569 /* ContentView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ContentView.swift; sourceTree = "<group>"; };
		A1234567890ABCDEF123456B /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		A1234567890ABCDEF123456D /* Preview Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = "Preview Assets.xcassets"; sourceTree = "<group>"; };
		A1234567890ABCDEF123456F /* AppDelegate.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AppDelegate.swift; sourceTree = "<group>"; };
		A1234567890ABCDEF1234571 /* ClaudeConfig.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ClaudeConfig.swift; sourceTree = "<group>"; };
		A1234567890ABCDEF1234573 /* ConfigService.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ConfigService.swift; sourceTree = "<group>"; };
		A1234567890ABCDEF1234575 /* StatusItemManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = StatusItemManager.swift; sourceTree = "<group>"; };
		A1234567890ABCDEF1234577 /* MenuBarView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MenuBarView.swift; sourceTree = "<group>"; };
		A1234567890ABCDEF1234579 /* MenuBarViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MenuBarViewModel.swift; sourceTree = "<group>"; };
		A1234567890ABCDEF123457B /* AppState.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AppState.swift; sourceTree = "<group>"; };
		A1234567890ABCDEF123457D /* KeychainService.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = KeychainService.swift; sourceTree = "<group>"; };
		A1234567890ABCDEF123457F /* ProcessService.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ProcessService.swift; sourceTree = "<group>"; };
		A1234567890ABCDEF1234581 /* Logger.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Logger.swift; sourceTree = "<group>"; };
		A1234567890ABCDEF1234582 /* ClaudeConfigManager.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = ClaudeConfigManager.entitlements; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		A1234567890ABCDEF1234583 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		A1234567890ABCDEF1234584 = {
			isa = PBXGroup;
			children = (
				A1234567890ABCDEF1234585 /* ClaudeConfigManager */,
				A1234567890ABCDEF1234586 /* Products */,
			);
			sourceTree = "<group>";
		};
		A1234567890ABCDEF1234585 /* ClaudeConfigManager */ = {
			isa = PBXGroup;
			children = (
				A1234567890ABCDEF1234587 /* App */,
				A1234567890ABCDEF1234588 /* Core */,
				A1234567890ABCDEF1234589 /* Features */,
				A1234567890ABCDEF123456B /* Assets.xcassets */,
				A1234567890ABCDEF1234582 /* ClaudeConfigManager.entitlements */,
				A1234567890ABCDEF123458A /* Preview Content */,
			);
			path = ClaudeConfigManager;
			sourceTree = "<group>";
		};
		A1234567890ABCDEF1234586 /* Products */ = {
			isa = PBXGroup;
			children = (
				A1234567890ABCDEF1234565 /* ClaudeConfigManager.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		A1234567890ABCDEF1234587 /* App */ = {
			isa = PBXGroup;
			children = (
				A1234567890ABCDEF1234566 /* ClaudeConfigManagerApp.swift */,
				A1234567890ABCDEF123456F /* AppDelegate.swift */,
				A1234567890ABCDEF123457B /* AppState.swift */,
			);
			path = App;
			sourceTree = "<group>";
		};
		A1234567890ABCDEF1234588 /* Core */ = {
			isa = PBXGroup;
			children = (
				A1234567890ABCDEF123458B /* Models */,
				A1234567890ABCDEF123458C /* Services */,
			);
			path = Core;
			sourceTree = "<group>";
		};
		A1234567890ABCDEF1234589 /* Features */ = {
			isa = PBXGroup;
			children = (
				A1234567890ABCDEF123458D /* MenuBar */,
				A1234567890ABCDEF1234569 /* ContentView.swift */,
			);
			path = Features;
			sourceTree = "<group>";
		};
		A1234567890ABCDEF123458A /* Preview Content */ = {
			isa = PBXGroup;
			children = (
				A1234567890ABCDEF123456D /* Preview Assets.xcassets */,
			);
			path = "Preview Content";
			sourceTree = "<group>";
		};
		A1234567890ABCDEF123458B /* Models */ = {
			isa = PBXGroup;
			children = (
				A1234567890ABCDEF1234571 /* ClaudeConfig.swift */,
			);
			path = Models;
			sourceTree = "<group>";
		};
		A1234567890ABCDEF123458C /* Services */ = {
			isa = PBXGroup;
			children = (
				A1234567890ABCDEF1234573 /* ConfigService.swift */,
				A1234567890ABCDEF123457D /* KeychainService.swift */,
				A1234567890ABCDEF123457F /* ProcessService.swift */,
				A1234567890ABCDEF1234581 /* Logger.swift */,
			);
			path = Services;
			sourceTree = "<group>";
		};
		A1234567890ABCDEF123458D /* MenuBar */ = {
			isa = PBXGroup;
			children = (
				A1234567890ABCDEF1234575 /* StatusItemManager.swift */,
				A1234567890ABCDEF1234577 /* MenuBarView.swift */,
				A1234567890ABCDEF1234579 /* MenuBarViewModel.swift */,
			);
			path = MenuBar;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		A1234567890ABCDEF123458E /* ClaudeConfigManager */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = A1234567890ABCDEF123458F /* Build configuration list for PBXNativeTarget "ClaudeConfigManager" */;
			buildPhases = (
				A1234567890ABCDEF1234590 /* Sources */,
				A1234567890ABCDEF1234583 /* Frameworks */,
				A1234567890ABCDEF1234591 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = ClaudeConfigManager;
			productName = ClaudeConfigManager;
			productReference = A1234567890ABCDEF1234565 /* ClaudeConfigManager.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		A1234567890ABCDEF1234592 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1500;
				LastUpgradeCheck = 1500;
				TargetAttributes = {
					A1234567890ABCDEF123458E = {
						CreatedOnToolsVersion = 15.0;
					};
				};
			};
			buildConfigurationList = A1234567890ABCDEF1234593 /* Build configuration list for PBXProject "ClaudeConfigManager" */;
			compatibilityVersion = "Xcode 14.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
				"zh-Hans",
			);
			mainGroup = A1234567890ABCDEF1234584;
			productRefGroup = A1234567890ABCDEF1234586 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				A1234567890ABCDEF123458E /* ClaudeConfigManager */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		A1234567890ABCDEF1234591 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				A1234567890ABCDEF123456C /* Preview Assets.xcassets in Resources */,
				A1234567890ABCDEF123456A /* Assets.xcassets in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		A1234567890ABCDEF1234590 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				A1234567890ABCDEF1234568 /* ContentView.swift in Sources */,
				A1234567890ABCDEF1234572 /* ConfigService.swift in Sources */,
				A1234567890ABCDEF1234574 /* StatusItemManager.swift in Sources */,
				A1234567890ABCDEF1234570 /* ClaudeConfig.swift in Sources */,
				A1234567890ABCDEF123457A /* AppState.swift in Sources */,
				A1234567890ABCDEF1234576 /* MenuBarView.swift in Sources */,
				A1234567890ABCDEF1234567 /* ClaudeConfigManagerApp.swift in Sources */,
				A1234567890ABCDEF123456E /* AppDelegate.swift in Sources */,
				A1234567890ABCDEF1234578 /* MenuBarViewModel.swift in Sources */,
				A1234567890ABCDEF123457C /* KeychainService.swift in Sources */,
				A1234567890ABCDEF123457E /* ProcessService.swift in Sources */,
				A1234567890ABCDEF1234580 /* Logger.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		A1234567890ABCDEF1234594 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MACOSX_DEPLOYMENT_TARGET = 10.15;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = macosx;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		A1234567890ABCDEF1234595 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MACOSX_DEPLOYMENT_TARGET = 10.15;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = macosx;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
			};
			name = Release;
		};
		A1234567890ABCDEF1234596 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = ClaudeConfigManager/ClaudeConfigManager.entitlements;
				CODE_SIGN_STYLE = Automatic;
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"ClaudeConfigManager/Preview Content\"";
				DEVELOPMENT_TEAM = "";
				ENABLE_HARDENED_RUNTIME = YES;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_CFBundleDisplayName = "Claude 配置管理器";
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.utilities";
				INFOPLIST_KEY_LSUIElement = YES;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
				);
				MACOSX_DEPLOYMENT_TARGET = 15.0;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.claude.configmanager;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
			};
			name = Debug;
		};
		A1234567890ABCDEF1234597 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = ClaudeConfigManager/ClaudeConfigManager.entitlements;
				CODE_SIGN_STYLE = Automatic;
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"ClaudeConfigManager/Preview Content\"";
				DEVELOPMENT_TEAM = "";
				ENABLE_HARDENED_RUNTIME = YES;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_CFBundleDisplayName = "Claude 配置管理器";
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.utilities";
				INFOPLIST_KEY_LSUIElement = YES;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
				);
				MACOSX_DEPLOYMENT_TARGET = 15.0;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.claude.configmanager;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		A1234567890ABCDEF123458F /* Build configuration list for PBXNativeTarget "ClaudeConfigManager" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				A1234567890ABCDEF1234596 /* Debug */,
				A1234567890ABCDEF1234597 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		A1234567890ABCDEF1234593 /* Build configuration list for PBXProject "ClaudeConfigManager" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				A1234567890ABCDEF1234594 /* Debug */,
				A1234567890ABCDEF1234595 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = A1234567890ABCDEF1234592 /* Project object */;
}
