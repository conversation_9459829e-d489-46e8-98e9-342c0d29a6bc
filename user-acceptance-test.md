# Claude 配置管理器 - 用户验收测试 (UAT)

## 概述

本文档定义了 Claude 配置管理器的用户验收测试标准，确保应用完全满足用户需求，能够成功替代原有的 `switch-claude.sh` 脚本。

## 测试目标

### 主要目标
1. **功能完整性**: 验证所有承诺功能正常工作
2. **易用性**: 确保普通用户能够轻松使用
3. **稳定性**: 保证应用在各种场景下稳定运行
4. **安全性**: 验证敏感信息得到妥善保护
5. **性能**: 确认应用性能优于原脚本

### 成功标准
- **功能验收率**: 100% 核心功能通过
- **用户满意度**: ≥ 90% 用户认为体验优于原脚本
- **任务完成率**: ≥ 95% 用户能独立完成基本任务
- **错误容忍度**: 用户能理解并处理 90% 的错误情况

## 测试用户群体

### 主要用户类型

#### 类型 1: 技术用户 (40%)
- **背景**: 熟悉命令行和原 shell 脚本
- **期望**: 功能强大、快速高效
- **关注点**: 高级功能、自定义选项

#### 类型 2: 设计师/创作者 (35%)
- **背景**: 使用 Claude 进行创作，不熟悉技术细节
- **期望**: 简单易用、界面友好
- **关注点**: 视觉反馈、操作简单

#### 类型 3: 团队协作者 (25%)
- **背景**: 团队环境使用，需要标准化配置
- **期望**: 配置共享、一致性
- **关注点**: 团队协作、配置同步

## UAT 测试场景

### 场景 1: 初次使用体验

#### 测试用例 1.1: 全新用户安装
**测试目标**: 验证新用户能够顺利开始使用

**前置条件**:
- macOS 系统已安装 Claude CLI
- 用户从未使用过配置管理工具
- 无现有配置文件

**测试步骤**:
1. 下载并安装 ClaudeConfigManager.app
2. 首次启动应用
3. 观察菜单栏是否出现应用图标
4. 点击菜单栏图标
5. 查看显示的信息和选项

**预期结果**:
- ✅ 应用成功启动且菜单栏出现图标
- ✅ 点击图标显示友好的"无配置文件"提示
- ✅ 提供配置文件目录的访问入口
- ✅ 显示简单的使用指导

**验收标准**:
- 用户能在 2 分钟内理解如何开始使用
- 界面提示清晰易懂
- 无技术术语困扰普通用户

#### 测试用例 1.2: 配置文件迁移
**测试目标**: 验证现有 shell 脚本用户的迁移体验

**前置条件**:
- 用户已使用 `switch-claude.sh`
- 存在多个 `*-settings.json` 配置文件
- 配置文件中包含 API Token

**测试步骤**:
1. 准备 3-5 个现有配置文件
2. 启动 ClaudeConfigManager.app
3. 观察配置自动发现过程
4. 检查 Token 迁移情况
5. 尝试切换配置

**预期结果**:
- ✅ 所有现有配置被自动发现
- ✅ Token 成功迁移到 Keychain
- ✅ 原配置文件中的 Token 被移除
- ✅ 配置切换功能正常工作
- ✅ 提供迁移状态反馈

**验收标准**:
- 100% 配置文件被正确识别
- Token 迁移过程无数据丢失
- 用户收到明确的迁移完成确认

### 场景 2: 日常使用体验

#### 测试用例 2.1: 配置切换操作
**测试目标**: 验证核心功能的易用性

**前置条件**:
- 已有 3 个有效配置文件
- 应用正常运行

**测试步骤**:
1. 点击菜单栏图标
2. 查看配置列表
3. 选择不同的配置
4. 观察状态变化
5. 验证切换结果

**预期结果**:
- ✅ 配置列表清晰显示，当前配置有明显标识
- ✅ 配置名称易于识别和区分
- ✅ 切换操作响应快速 (< 1 秒)
- ✅ 状态更新及时准确
- ✅ 错误情况有友好提示

**验收标准**:
- 用户能在 10 秒内完成配置切换
- 操作过程直观明了，无需说明文档
- 状态反馈准确及时

#### 测试用例 2.2: 错误处理体验
**测试目标**: 验证错误情况下的用户体验

**测试步骤**:
1. 创建格式错误的配置文件
2. 模拟 Keychain 访问被拒绝
3. 删除配置目录权限
4. 观察应用反应和错误提示

**预期结果**:
- ✅ 错误信息用中文显示
- ✅ 提供具体的解决建议
- ✅ 应用不崩溃，可恢复
- ✅ 用户能理解问题所在

**验收标准**:
- 90% 错误信息用户能理解
- 提供的解决方案切实可行
- 应用在错误情况下保持稳定

### 场景 3: 高级使用场景

#### 测试用例 3.1: 进程管理功能
**测试目标**: 验证 Claude CLI 进程管理功能

**测试步骤**:
1. 查看 Claude 进程状态
2. 使用应用停止 Claude 进程
3. 使用应用启动 Claude 进程
4. 在进程运行时切换配置
5. 观察进程自动重启

**预期结果**:
- ✅ 进程状态显示准确
- ✅ 启动/停止功能工作正常
- ✅ 配置切换时自动重启进程
- ✅ 操作反馈及时

**验收标准**:
- 进程管理操作 100% 成功
- 状态显示与实际情况一致
- 自动重启机制可靠

#### 测试用例 3.2: 安全功能验证
**测试目标**: 验证 Token 安全处理

**测试步骤**:
1. 查看界面中的 Token 显示
2. 检查 Keychain 中的 Token 存储
3. 验证配置文件中 Token 已移除
4. 测试 Token 脱敏显示

**预期结果**:
- ✅ 界面显示脱敏的 Token (sk-ant-****)
- ✅ 完整 Token 安全存储在 Keychain
- ✅ 配置文件中不再包含明文 Token
- ✅ 无法通过界面泄露完整 Token

**验收标准**:
- Token 安全性 100% 符合预期
- 脱敏显示方式用户友好
- Keychain 集成无安全风险

### 场景 4: 性能和稳定性

#### 测试用例 4.1: 大量配置处理
**测试目标**: 验证应用处理大量配置的能力

**前置条件**:
- 准备 20+ 个配置文件

**测试步骤**:
1. 启动应用
2. 观察配置加载时间
3. 测试配置列表滚动
4. 频繁切换不同配置
5. 监控内存和 CPU 使用

**预期结果**:
- ✅ 配置加载时间合理 (< 5 秒)
- ✅ 界面响应流畅
- ✅ 内存使用稳定 (< 100MB)
- ✅ 无明显性能下降

**验收标准**:
- 支持至少 50 个配置文件
- 界面操作始终流畅
- 资源使用保持在合理范围

#### 测试用例 4.2: 长时间运行稳定性
**测试目标**: 验证应用长期运行的稳定性

**测试步骤**:
1. 启动应用并持续运行 4+ 小时
2. 每 30 分钟进行一次配置切换
3. 监控内存使用变化
4. 检查应用响应性
5. 观察是否有崩溃或异常

**预期结果**:
- ✅ 应用持续稳定运行
- ✅ 无内存泄漏现象
- ✅ 响应速度保持稳定
- ✅ 无崩溃或异常

**验收标准**:
- 连续运行 8 小时无问题
- 内存使用增长 < 10%
- 所有功能保持正常

## 对比验证

### 与原 shell 脚本对比

#### 功能对比表

| 功能 | switch-claude.sh | ClaudeConfigManager | 改进评价 |
|-----|-----------------|-------------------|----------|
| 配置切换 | 命令行输入配置名 | 图形界面选择 | ⭐⭐⭐⭐⭐ 显著改进 |
| 配置发现 | 手动指定路径 | 自动扫描 | ⭐⭐⭐⭐⭐ 显著改进 |
| Token 管理 | 明文存储 | Keychain 加密 | ⭐⭐⭐⭐⭐ 显著改进 |
| 状态显示 | 无状态显示 | 实时状态更新 | ⭐⭐⭐⭐⭐ 新增功能 |
| 错误处理 | 简单错误信息 | 详细指导信息 | ⭐⭐⭐⭐ 明显改进 |
| 进程管理 | 基础重启 | 完整进程控制 | ⭐⭐⭐⭐⭐ 显著改进 |
| 易用性 | 需要技术背景 | 普通用户友好 | ⭐⭐⭐⭐⭐ 显著改进 |

#### 用户体验对比

**原脚本使用流程**:
1. 打开终端
2. 输入 `switch-claude.sh config-name`
3. 等待脚本执行
4. 无直观反馈

**新应用使用流程**:
1. 点击菜单栏图标
2. 从列表中选择配置
3. 即时切换完成
4. 状态实时更新

**改进评估**: 操作步骤减少 50%，用户友好度提升 300%

### 迁移成功验证

#### 迁移检查清单

- [ ] **配置兼容性**: 所有现有配置文件正常工作
- [ ] **功能等价性**: 所有原脚本功能都可通过新应用实现
- [ ] **性能优势**: 新应用响应速度快于原脚本
- [ ] **安全改进**: Token 安全性显著提升
- [ ] **易用性提升**: 非技术用户能够使用
- [ ] **稳定性保证**: 错误处理能力强于原脚本

#### 回滚测试

**测试目标**: 确保在需要时能够回滚到原脚本

**测试步骤**:
1. 使用新应用管理配置
2. 停止新应用
3. 尝试使用原脚本
4. 验证配置文件完整性
5. 确认 Token 可用性

**预期结果**:
- ✅ 配置文件格式完全兼容
- ✅ 原脚本功能正常
- ✅ 无数据丢失或损坏

## 测试执行计划

### 测试阶段

#### 阶段 1: 内部验收 (3 天)
**参与者**: 开发团队
**目标**: 验证基本功能完整性
**测试用例**: 所有自动化测试 + 核心手动测试

#### 阶段 2: Alpha 测试 (1 周)
**参与者**: 5-8 名技术用户
**目标**: 验证高级功能和稳定性
**反馈收集**: 功能缺陷、性能问题、改进建议

#### 阶段 3: Beta 测试 (2 周)
**参与者**: 15-20 名不同类型用户
**目标**: 验证易用性和广泛兼容性
**反馈收集**: 用户体验、使用习惯、满意度评估

#### 阶段 4: 发布候选验证 (3 天)
**参与者**: 核心用户群体
**目标**: 最终验证发布就绪性
**验收标准**: 所有关键问题已解决

### 测试数据收集

#### 定量指标
- **任务完成率**: 用户能独立完成基本任务的比例
- **错误率**: 用户操作过程中出现错误的频率
- **完成时间**: 完成特定任务所需的时间
- **学习曲线**: 用户掌握应用使用的时间

#### 定性反馈
- **满意度评分**: 1-10 分用户体验评分
- **功能偏好**: 用户最喜欢/最不喜欢的功能
- **改进建议**: 用户提出的具体改进建议
- **推荐意愿**: 用户是否愿意推荐给他人

### 验收标准

#### 必须满足的条件
1. **功能完整性**: 100% 核心功能正常工作
2. **稳定性**: 无严重崩溃或数据丢失
3. **安全性**: Token 安全处理 100% 正确
4. **兼容性**: 支持承诺的所有 macOS 版本

#### 期望达到的目标
1. **用户满意度**: ≥ 90% 用户评分 7 分以上
2. **任务完成率**: ≥ 95% 用户能独立完成基本任务
3. **性能优势**: 操作速度比原脚本快 50% 以上
4. **推荐率**: ≥ 80% 用户愿意推荐给他人

## 测试记录模板

### 测试会话记录

```
测试会话 ID: UAT-001
测试日期: 2025-07-27
测试员: [姓名]
用户类型: [技术用户/设计师/团队协作者]
系统环境: macOS [版本] / [架构]

测试场景: [场景名称]
前置条件: [详细描述]

执行步骤:
1. [步骤 1] - 结果: [成功/失败] - 备注: [详细说明]
2. [步骤 2] - 结果: [成功/失败] - 备注: [详细说明]
...

问题记录:
- [问题 1]: 严重性 [Critical/Major/Minor/Trivial] - 描述: [详细说明]
- [问题 2]: 严重性 [Critical/Major/Minor/Trivial] - 描述: [详细说明]

用户反馈:
- 满意度评分: [1-10]
- 最喜欢的功能: [描述]
- 最不满意的方面: [描述]
- 改进建议: [详细建议]

总体评价: [通过/有条件通过/不通过]
```

### 缺陷报告模板

```
缺陷 ID: BUG-001
发现日期: 2025-07-27
报告者: [姓名]
严重性: [Critical/Major/Minor/Trivial]
优先级: [P1/P2/P3/P4]

缺陷摘要: [简短描述]

重现步骤:
1. [步骤 1]
2. [步骤 2]
3. [步骤 3]

预期结果: [应该发生什么]
实际结果: [实际发生了什么]

环境信息:
- macOS 版本: [版本]
- 应用版本: [版本]
- 其他相关信息: [详细说明]

附件: [截图、日志文件等]
```

## 发布决策矩阵

### 发布条件

| 条件类别 | 要求 | 权重 | 状态 |
|---------|------|------|------|
| 核心功能 | 100% 工作正常 | 40% | [ ] |
| 稳定性 | 无 Critical/Major 缺陷 | 30% | [ ] |
| 用户体验 | 满意度 ≥ 90% | 20% | [ ] |
| 性能 | 达到基准要求 | 10% | [ ] |

### 决策规则

- **绿灯发布**: 所有条件满足
- **黄灯有条件发布**: 核心功能和稳定性满足，其他条件部分满足
- **红灯延期发布**: 核心功能或稳定性存在问题

---

**文档版本**: 1.0
**创建日期**: 2025-07-27
**审核者**: [项目负责人]
**批准者**: [产品负责人]