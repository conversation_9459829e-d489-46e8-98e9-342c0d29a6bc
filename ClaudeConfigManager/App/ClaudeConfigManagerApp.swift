import SwiftUI

@main
struct ClaudeConfigManagerApp: App {
    @StateObject private var appState = AppState()
    @NSApplicationDelegateAdaptor(AppDelegate.self) var appDelegate
    
    var body: some Scene {
        Settings {
            ContentView()
                .environmentObject(appState)
                .onAppear {
                    appDelegate.setAppState(appState)
                }
        }
    }
}