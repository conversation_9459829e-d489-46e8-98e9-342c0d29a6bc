import AppKit
import SwiftUI

class AppDelegate: NSObject, NSApplicationDelegate {
    private var appState: AppState?
    private var statusItemManager: StatusItemManager?
    
    func setAppState(_ appState: AppState) {
        self.appState = appState
    }
    
    func applicationDidFinishLaunching(_ notification: Notification) {
        // 隐藏 Dock 图标
        NSApp.setActivationPolicy(.accessory)
        
        // 如果 AppState 还没有设置，创建一个临时的
        if appState == nil {
            appState = AppState()
        }
        
        // 初始化状态栏管理器
        if let appState = appState {
            statusItemManager = StatusItemManager(appState: appState)
            
            // 加载配置
            Task {
                await appState.loadConfigs()
            }
        }
    }
    
    func applicationWillTerminate(_ notification: Notification) {
        statusItemManager = nil
    }
}