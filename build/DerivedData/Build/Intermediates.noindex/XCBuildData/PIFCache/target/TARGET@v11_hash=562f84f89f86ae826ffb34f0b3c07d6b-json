{"buildConfigurations": [{"buildSettings": {"ASSETCATALOG_COMPILER_APPICON_NAME": "AppIcon", "ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME": "AccentColor", "CODE_SIGN_ENTITLEMENTS": "ClaudeConfigManager/ClaudeConfigManager.entitlements", "CODE_SIGN_STYLE": "Automatic", "COMBINE_HIDPI_IMAGES": "YES", "CURRENT_PROJECT_VERSION": "1", "DEVELOPMENT_ASSET_PATHS": "\"ClaudeConfigManager/Preview Content\"", "DEVELOPMENT_TEAM": "", "ENABLE_HARDENED_RUNTIME": "YES", "ENABLE_PREVIEWS": "YES", "GENERATE_INFOPLIST_FILE": "YES", "INFOPLIST_KEY_CFBundleDisplayName": "Claude 配置管理器", "INFOPLIST_KEY_LSApplicationCategoryType": "public.app-category.utilities", "INFOPLIST_KEY_LSUIElement": "YES", "INFOPLIST_KEY_NSHumanReadableCopyright": "", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/../Frameworks", "MACOSX_DEPLOYMENT_TARGET": "15.0", "MARKETING_VERSION": "1.0", "PRODUCT_BUNDLE_IDENTIFIER": "com.claude.configmanager", "PRODUCT_NAME": "$(TARGET_NAME)", "SWIFT_EMIT_LOC_STRINGS": "YES", "SWIFT_VERSION": "5.0"}, "guid": "dd5074e12287a3259fdfd8e81b104e7adce6fb0411c00f9cc73bac6359a4f714", "name": "Debug"}, {"buildSettings": {"ASSETCATALOG_COMPILER_APPICON_NAME": "AppIcon", "ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME": "AccentColor", "CODE_SIGN_ENTITLEMENTS": "ClaudeConfigManager/ClaudeConfigManager.entitlements", "CODE_SIGN_STYLE": "Automatic", "COMBINE_HIDPI_IMAGES": "YES", "CURRENT_PROJECT_VERSION": "1", "DEVELOPMENT_ASSET_PATHS": "\"ClaudeConfigManager/Preview Content\"", "DEVELOPMENT_TEAM": "", "ENABLE_HARDENED_RUNTIME": "YES", "ENABLE_PREVIEWS": "YES", "GENERATE_INFOPLIST_FILE": "YES", "INFOPLIST_KEY_CFBundleDisplayName": "Claude 配置管理器", "INFOPLIST_KEY_LSApplicationCategoryType": "public.app-category.utilities", "INFOPLIST_KEY_LSUIElement": "YES", "INFOPLIST_KEY_NSHumanReadableCopyright": "", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/../Frameworks", "MACOSX_DEPLOYMENT_TARGET": "15.0", "MARKETING_VERSION": "1.0", "PRODUCT_BUNDLE_IDENTIFIER": "com.claude.configmanager", "PRODUCT_NAME": "$(TARGET_NAME)", "SWIFT_EMIT_LOC_STRINGS": "YES", "SWIFT_VERSION": "5.0"}, "guid": "dd5074e12287a3259fdfd8e81b104e7a9f8f71f5fa83e191e223f6a90a23339b", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "dd5074e12287a3259fdfd8e81b104e7af8c41665ca07c8804cff3910aa225f3a", "guid": "dd5074e12287a3259fdfd8e81b104e7a38517afa0d85cbe6634994147767e764"}, {"fileReference": "dd5074e12287a3259fdfd8e81b104e7a4a856f509760751d0704d02750449f4b", "guid": "dd5074e12287a3259fdfd8e81b104e7a7528520dfeb775af0c689e3b94f00fdd"}, {"fileReference": "dd5074e12287a3259fdfd8e81b104e7aa345c307fd58860075435e439d2d2231", "guid": "dd5074e12287a3259fdfd8e81b104e7a6a7b70a52d648e9510f25d72f190cd52"}, {"fileReference": "dd5074e12287a3259fdfd8e81b104e7adc11d80825c54bb9b2c9941f141ca6b8", "guid": "dd5074e12287a3259fdfd8e81b104e7a7482550bd4d6352044de40e68d0cb918"}, {"fileReference": "dd5074e12287a3259fdfd8e81b104e7a852f44f1239f9b0e5db28b7ea75bedfe", "guid": "dd5074e12287a3259fdfd8e81b104e7a1525afa63c631e1300da9e946bfadd61"}, {"fileReference": "dd5074e12287a3259fdfd8e81b104e7adf115f6ea0eca3638b972184259cbdc5", "guid": "dd5074e12287a3259fdfd8e81b104e7a3d7f8be26eed7783035b63ce495eda46"}, {"fileReference": "dd5074e12287a3259fdfd8e81b104e7a619a65d77c42f0638ad8bc25bbaf7530", "guid": "dd5074e12287a3259fdfd8e81b104e7af78008e7cf0186a1731e35167d750d84"}, {"fileReference": "dd5074e12287a3259fdfd8e81b104e7a19e890c1d413b86162badb6654a6135e", "guid": "dd5074e12287a3259fdfd8e81b104e7a352bb9282d68a3cc7a4f77b021d7b2f8"}, {"fileReference": "dd5074e12287a3259fdfd8e81b104e7aa33b65265de3f4158b74bcd9e869b0dc", "guid": "dd5074e12287a3259fdfd8e81b104e7ac897186bad3b594be02a26bd1528bf5d"}, {"fileReference": "dd5074e12287a3259fdfd8e81b104e7ac75c497bfd121ddba1620dd95982feb4", "guid": "dd5074e12287a3259fdfd8e81b104e7a48b5baf14852c32aca082ec73a28bb80"}, {"fileReference": "dd5074e12287a3259fdfd8e81b104e7ad3a4219fd900159e27464d408fa91d88", "guid": "dd5074e12287a3259fdfd8e81b104e7a74e1a42fe91699a3c269465aecd70692"}, {"fileReference": "dd5074e12287a3259fdfd8e81b104e7a7ff5cd6a85bb855a60e5e8db74b72fec", "guid": "dd5074e12287a3259fdfd8e81b104e7a6798bf18fd6bb2e17bc37059e5d19a30"}], "guid": "dd5074e12287a3259fdfd8e81b104e7a0f9e96a757bd50cbd035d1f64e6ee70c", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "dd5074e12287a3259fdfd8e81b104e7a6f4fe2b0f89c3ce804abf912838e8135", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"fileReference": "dd5074e12287a3259fdfd8e81b104e7a57827fe2102bac0de2d14af2e99b11cb", "guid": "dd5074e12287a3259fdfd8e81b104e7a280baab3c42ed6a016c7559b6b525ad8"}, {"fileReference": "dd5074e12287a3259fdfd8e81b104e7a8b7ba8e5f5069ed840688357dc4d2028", "guid": "dd5074e12287a3259fdfd8e81b104e7ac38b911148651b1f79b1100ce252bca9"}], "guid": "dd5074e12287a3259fdfd8e81b104e7aed27923542b3daf326a7043d20f3460f", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "dd5074e12287a3259fdfd8e81b104e7a6c536ad9220eec982aaad32eed92de9e", "name": "ClaudeConfigManager", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "dd5074e12287a3259fdfd8e81b104e7a2fa32a9da776d2aca450496aa4499286", "name": "ClaudeConfigManager.app", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.application", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "$(PRODUCT_BUNDLE_IDENTIFIER)", "configurationName": "Debug", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "$(PRODUCT_BUNDLE_IDENTIFIER)", "configurationName": "Release", "provisioningStyle": 0}], "type": "standard"}