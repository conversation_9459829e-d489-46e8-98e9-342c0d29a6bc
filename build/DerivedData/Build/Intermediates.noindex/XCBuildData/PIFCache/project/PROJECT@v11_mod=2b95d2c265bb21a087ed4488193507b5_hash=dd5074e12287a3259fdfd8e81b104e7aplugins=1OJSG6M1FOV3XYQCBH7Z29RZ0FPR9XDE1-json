{"appPreferencesBuildSettings": {}, "buildConfigurations": [{"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++20", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "ENABLE_TESTABILITY": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu11", "GCC_DYNAMIC_NO_PIC": "NO", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_OPTIMIZATION_LEVEL": "0", "GCC_PREPROCESSOR_DEFINITIONS": "DEBUG=1 $(inherited)", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "LOCALIZATION_PREFERS_STRING_CATALOGS": "YES", "MACOSX_DEPLOYMENT_TARGET": "10.15", "MTL_ENABLE_DEBUG_INFO": "INCLUDE_SOURCE", "MTL_FAST_MATH": "YES", "ONLY_ACTIVE_ARCH": "YES", "SDKROOT": "macosx", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "DEBUG", "SWIFT_OPTIMIZATION_LEVEL": "-<PERSON><PERSON>"}, "guid": "dd5074e12287a3259fdfd8e81b104e7ab53020d31a580b852459aa2dab53b482", "name": "Debug"}, {"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++20", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf-with-dsym", "ENABLE_NS_ASSERTIONS": "NO", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu11", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "LOCALIZATION_PREFERS_STRING_CATALOGS": "YES", "MACOSX_DEPLOYMENT_TARGET": "10.15", "MTL_ENABLE_DEBUG_INFO": "NO", "MTL_FAST_MATH": "YES", "SDKROOT": "macosx", "SWIFT_COMPILATION_MODE": "wholemodule", "SWIFT_OPTIMIZATION_LEVEL": "-O"}, "guid": "dd5074e12287a3259fdfd8e81b104e7ab9cb9fbfa59dd80c74dcc37383cdb8c4", "name": "Release"}], "classPrefix": "", "defaultConfigurationName": "Release", "developmentRegion": "en", "groupTree": {"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "dd5074e12287a3259fdfd8e81b104e7a619a65d77c42f0638ad8bc25bbaf7530", "path": "ClaudeConfigManagerApp.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "dd5074e12287a3259fdfd8e81b104e7a19e890c1d413b86162badb6654a6135e", "path": "AppDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "dd5074e12287a3259fdfd8e81b104e7a852f44f1239f9b0e5db28b7ea75bedfe", "path": "AppState.swift", "sourceTree": "<group>", "type": "file"}], "guid": "dd5074e12287a3259fdfd8e81b104e7aaa1ce62a3c4971efb15c9687553160b1", "name": "App", "path": "App", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "dd5074e12287a3259fdfd8e81b104e7adc11d80825c54bb9b2c9941f141ca6b8", "path": "ClaudeConfig.swift", "sourceTree": "<group>", "type": "file"}], "guid": "dd5074e12287a3259fdfd8e81b104e7ab80cc698c00ffd64fdd2585d5e538d38", "name": "Models", "path": "Models", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "dd5074e12287a3259fdfd8e81b104e7a4a856f509760751d0704d02750449f4b", "path": "ConfigService.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "dd5074e12287a3259fdfd8e81b104e7ac75c497bfd121ddba1620dd95982feb4", "path": "KeychainService.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "dd5074e12287a3259fdfd8e81b104e7ad3a4219fd900159e27464d408fa91d88", "path": "ProcessService.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "dd5074e12287a3259fdfd8e81b104e7a7ff5cd6a85bb855a60e5e8db74b72fec", "path": "Logger.swift", "sourceTree": "<group>", "type": "file"}], "guid": "dd5074e12287a3259fdfd8e81b104e7a7952afe7b18e32c59754b420cd21fc65", "name": "Services", "path": "Services", "sourceTree": "<group>", "type": "group"}], "guid": "dd5074e12287a3259fdfd8e81b104e7afc3441e86c91a978b682e82ca94e53a5", "name": "Core", "path": "Core", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "dd5074e12287a3259fdfd8e81b104e7aa345c307fd58860075435e439d2d2231", "path": "StatusItemManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "dd5074e12287a3259fdfd8e81b104e7adf115f6ea0eca3638b972184259cbdc5", "path": "MenuBarView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "dd5074e12287a3259fdfd8e81b104e7aa33b65265de3f4158b74bcd9e869b0dc", "path": "MenuBarViewModel.swift", "sourceTree": "<group>", "type": "file"}], "guid": "dd5074e12287a3259fdfd8e81b104e7a2e12f73068fc90ed1d6299772c4fd35f", "name": "<PERSON><PERSON><PERSON><PERSON>", "path": "<PERSON><PERSON><PERSON><PERSON>", "sourceTree": "<group>", "type": "group"}, {"fileType": "sourcecode.swift", "guid": "dd5074e12287a3259fdfd8e81b104e7af8c41665ca07c8804cff3910aa225f3a", "path": "ContentView.swift", "sourceTree": "<group>", "type": "file"}], "guid": "dd5074e12287a3259fdfd8e81b104e7adcb0815f1b8262b2cd805b8a0ab23e32", "name": "Features", "path": "Features", "sourceTree": "<group>", "type": "group"}, {"fileType": "folder.assetcatalog", "guid": "dd5074e12287a3259fdfd8e81b104e7a8b7ba8e5f5069ed840688357dc4d2028", "path": "Assets.xcassets", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.entitlements", "guid": "dd5074e12287a3259fdfd8e81b104e7a48a77a38c9617bb675c9072fab1c1cf2", "path": "ClaudeConfigManager.entitlements", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "folder.assetcatalog", "guid": "dd5074e12287a3259fdfd8e81b104e7a57827fe2102bac0de2d14af2e99b11cb", "path": "Preview Assets.xcassets", "sourceTree": "<group>", "type": "file"}], "guid": "dd5074e12287a3259fdfd8e81b104e7a0f979441edb322cb285d7fb14fcbb42c", "name": "Preview Content", "path": "Preview Content", "sourceTree": "<group>", "type": "group"}], "guid": "dd5074e12287a3259fdfd8e81b104e7ad66da3f89396d02f5032a3f22f5a60ac", "name": "ClaudeConfigManager", "path": "ClaudeConfigManager", "sourceTree": "<group>", "type": "group"}, {"guid": "dd5074e12287a3259fdfd8e81b104e7a74e76797a863636d6f6bdfd99fb7cd90", "name": "Products", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "dd5074e12287a3259fdfd8e81b104e7ae60c5489ca77b9d4c1abec50cdd776ec", "name": "ClaudeConfigManager", "path": "", "sourceTree": "<group>", "type": "group"}, "guid": "dd5074e12287a3259fdfd8e81b104e7a", "path": "/Users/<USER>/XcodeProjects/ClaudeConfigManager/ClaudeConfigManager.xcodeproj", "projectDirectory": "/Users/<USER>/XcodeProjects/ClaudeConfigManager", "targets": ["TARGET@v11_hash=562f84f89f86ae826ffb34f0b3c07d6b"]}