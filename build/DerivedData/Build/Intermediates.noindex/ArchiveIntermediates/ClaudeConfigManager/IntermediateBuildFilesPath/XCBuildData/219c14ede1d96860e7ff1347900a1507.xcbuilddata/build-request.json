{"buildCommand": {"command": "build", "skipDependencies": false, "style": "buildAndRun"}, "configuredTargets": [{"guid": "dd5074e12287a3259fdfd8e81b104e7a6c536ad9220eec982aaad32eed92de9e"}], "containerPath": "/Users/<USER>/XcodeProjects/ClaudeConfigManager/ClaudeConfigManager.xcodeproj", "continueBuildingAfterErrors": false, "dependencyScope": "workspace", "enableIndexBuildArena": false, "hideShellScriptEnvironment": false, "parameters": {"action": "install", "activeArchitecture": "x86_64", "activeRunDestination": {"disableOnlyActiveArch": false, "platform": "macosx", "sdk": "macosx15.5", "sdkVariant": "macos", "supportedArchitectures": ["x86_64h", "x86_64"], "targetArchitecture": "x86_64"}, "arenaInfo": {"buildIntermediatesPath": "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex", "buildProductsPath": "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Products", "derivedDataPath": "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData", "indexDataStoreFolderPath": "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Index.noindex/DataStore", "indexEnableDataStore": true, "indexPCHPath": "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Index.noindex/PrecompiledHeaders", "pchPath": "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/PrecompiledHeaders"}, "configurationName": "Release", "overrides": {"commandLine": {"table": {}}, "synthesized": {"table": {"ACTION": "install", "ASSET_PACK_FOLDER_PATH": "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/OnDemandResources", "ASSETCATALOG_COMPILER_FLATTENED_APP_ICON_PATH": "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/BuildProductsPath/ProductIcon.png", "DEPLOYMENT_LOCATION": "YES", "DEPLOYMENT_POSTPROCESSING": "YES", "DSTROOT": "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation", "EMBED_ASSET_PACKS_IN_PRODUCT_BUNDLE": "NO", "ENABLE_PREVIEWS": "NO", "ENABLE_SIGNATURE_AGGREGATION": "YES", "ENABLE_XOJIT_PREVIEWS": "YES", "INDEX_ENABLE_DATA_STORE": "NO", "MESSAGES_APPLICATION_EXTENSION_SUPPORT_FOLDER_PATH": "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/BuildProductsPath/MessagesApplicationExtensionSupport", "MESSAGES_APPLICATION_SUPPORT_FOLDER_PATH": "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/BuildProductsPath/MessagesApplicationSupport", "OBJROOT": "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath", "ONLY_ACTIVE_ARCH": "YES", "SHARED_PRECOMPS_DIR": "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/PrecompiledHeaders", "SIGNATURE_METADATA_FOLDER_PATH": "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/BuildProductsPath/Signatures", "SWIFT_STDLIB_TOOL_UNSIGNED_DESTINATION_DIR": "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/BuildProductsPath/SwiftSupport", "SYMROOT": "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/BuildProductsPath", "WATCHKIT_2_SUPPORT_FOLDER_PATH": "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/BuildProductsPath/WatchKitSupport2"}}}}, "schemeCommand": "archive", "showNonLoggedProgress": true, "useDryRun": false, "useImplicitDependencies": true, "useLegacyBuildLocations": false, "useParallelTargets": true}