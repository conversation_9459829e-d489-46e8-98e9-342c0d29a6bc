{"client": {"name": "basic", "version": 0, "file-system": "device-agnostic", "perform-ownership-analysis": "no"}, "targets": {"": ["<all>"]}, "nodes": {"/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/BuildProductsPath": {"is-mutated": true}, "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/BuildProductsPath/Release": {"is-mutated": true}, "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation": {"is-mutated": true}, "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app": {"is-mutated": true}, "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app/Contents/MacOS/ClaudeConfigManager": {"is-mutated": true}, "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath": {"is-mutated": true}, "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/EagerLinkingTBDs/Release": {"is-mutated": true}, "<TRIGGER: CodeSign /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app>": {"is-command-timestamp": true}, "<TRIGGER: Ld /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app/Contents/MacOS/ClaudeConfigManager normal>": {"is-command-timestamp": true}, "<TRIGGER: MkDir /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app>": {"is-command-timestamp": true}, "<TRIGGER: SetMode u+w,go-w,a+rX /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app>": {"is-command-timestamp": true}, "<TRIGGER: SetOwnerAndGroup xiaozhaodong:staff /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app>": {"is-command-timestamp": true}, "<TRIGGER: Strip /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app/Contents/MacOS/ClaudeConfigManager>": {"is-command-timestamp": true}, "<TRIGGER: Validate /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app>": {"is-command-timestamp": true}}, "commands": {"<all>": {"tool": "phony", "inputs": ["/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app/Contents", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app/Contents/MacOS", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app/_CodeSignature", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ExtractedAppShortcutsMetadata.stringsdata", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/EagerLinkingTBDs/Release", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/SDKStatCaches.noindex/macosx15.5-24F74-8254517eb8b97d462ccbc072ba9094c9.sdkstatcache", "<target-ClaudeConfigManager-****************************************************************--begin-scanning>", "<target-ClaudeConfigManager-****************************************************************--end>", "<target-ClaudeConfigManager-****************************************************************--linker-inputs-ready>", "<target-ClaudeConfigManager-****************************************************************--modules-ready>", "<workspace-Release-macosx15.5-macos--stale-file-removal>"], "outputs": ["<all>"]}, "<target-ClaudeConfigManager-****************************************************************-Release-macosx--x86_64-build-headers-stale-file-removal>": {"tool": "stale-file-removal", "expectedOutputs": ["/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app/Contents/MacOS/ClaudeConfigManager", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app/_CodeSignature", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/assetcatalog_output/thinned", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/assetcatalog_dependencies_thinned", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/assetcatalog_generated_info.plist_thinned", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/assetcatalog_output/unthinned", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/assetcatalog_dependencies_unthinned", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/assetcatalog_generated_info.plist_unthinned", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ExtractedAppShortcutsMetadata.stringsdata", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/DerivedSources/GeneratedAssetSymbols.h", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/DerivedSources/GeneratedAssetSymbols-Index.plist", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/BuildProductsPath/Release/ClaudeConfigManager.app.dSYM/Contents/Resources/DWARF/ClaudeConfigManager", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/assetcatalog_generated_info.plist", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app/Contents/Resources/Assets.car", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/assetcatalog_signature", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app/Contents", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app/Contents/MacOS", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app/Contents/Resources", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/assetcatalog_output/thinned", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/assetcatalog_output/unthinned", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app/Contents/Info.plist", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app/Contents/PkgInfo", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/ClaudeConfigManager.app.xcent", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/ClaudeConfigManager.app.xcent.der", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app/Contents/MacOS/ClaudeConfigManager", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager Swift Compilation Finished", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/BuildProductsPath/Release/ClaudeConfigManager.app", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/BuildProductsPath/Release/ClaudeConfigManager.swiftmodule/x86_64-apple-macos.abi.json", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/BuildProductsPath/Release/ClaudeConfigManager.swiftmodule/x86_64-apple-macos.swiftdoc", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/BuildProductsPath/Release/ClaudeConfigManager.swiftmodule/x86_64-apple-macos.swiftmodule", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app/Contents/MacOS/ClaudeConfigManager", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager_lto.o", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager_dependency_info.dat", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ContentView.o", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ConfigService.o", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/StatusItemManager.o", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfig.o", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/AppState.o", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/MenuBarView.o", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManagerApp.o", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/AppDelegate.o", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/MenuBarViewModel.o", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/KeychainService.o", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ProcessService.o", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/Logger.o", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/GeneratedAssetSymbols.o", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ContentView.stringsdata", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ConfigService.stringsdata", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/StatusItemManager.stringsdata", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfig.stringsdata", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/AppState.stringsdata", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/MenuBarView.stringsdata", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManagerApp.stringsdata", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/AppDelegate.stringsdata", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/MenuBarViewModel.stringsdata", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/KeychainService.stringsdata", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ProcessService.stringsdata", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/Logger.stringsdata", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager-master.swiftconstvalues", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager.swiftmodule", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager.swiftsourceinfo", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager.abi.json", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager-Swift.h", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager.swiftdoc", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/DerivedSources/ClaudeConfigManager-Swift.h", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/ClaudeConfigManager-all-non-framework-target-headers.hmap", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/ClaudeConfigManager-all-target-headers.hmap", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/ClaudeConfigManager-generated-files.hmap", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/ClaudeConfigManager-own-target-headers.hmap", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/ClaudeConfigManager-project-headers.hmap", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/ClaudeConfigManager.DependencyMetadataFileList", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/ClaudeConfigManager.DependencyStaticMetadataFileList", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/ClaudeConfigManager.hmap", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/DerivedSources/Entitlements.plist", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager-OutputFileMap.json", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager.LinkFileList", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager.SwiftConstValuesFileList", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager.SwiftFileList", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager_const_extract_protocols.json", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/empty-ClaudeConfigManager.plist"], "roots": ["/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/BuildProductsPath"], "outputs": ["<target-ClaudeConfigManager-****************************************************************-Release-macosx--x86_64-build-headers-stale-file-removal>"]}, "<workspace-Release-macosx15.5-macos--stale-file-removal>": {"tool": "stale-file-removal", "expectedOutputs": ["/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager-dd5074e12287a3259fdfd8e81b104e7a-VFS/all-product-headers.yaml"], "outputs": ["<workspace-Release-macosx15.5-macos--stale-file-removal>"]}, "P0:::ClangStatCache /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang-stat-cache /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/SDKStatCaches.noindex/macosx15.5-24F74-8254517eb8b97d462ccbc072ba9094c9.sdkstatcache": {"tool": "shell", "description": "ClangStatCache /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang-stat-cache /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/SDKStatCaches.noindex/macosx15.5-24F74-8254517eb8b97d462ccbc072ba9094c9.sdkstatcache", "inputs": [], "outputs": ["/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/SDKStatCaches.noindex/macosx15.5-24F74-8254517eb8b97d462ccbc072ba9094c9.sdkstatcache", "<ClangStatCache /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/SDKStatCaches.noindex/macosx15.5-24F74-8254517eb8b97d462ccbc072ba9094c9.sdkstatcache>"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang-stat-cache", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "-o", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/SDKStatCaches.noindex/macosx15.5-24F74-8254517eb8b97d462ccbc072ba9094c9.sdkstatcache"], "env": {}, "always-out-of-date": true, "working-directory": "/Users/<USER>/XcodeProjects/ClaudeConfigManager/ClaudeConfigManager.xcodeproj", "signature": "61261f3983edcf1115869017aab5b4d7"}, "P0:::CreateBuildDirectory /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/BuildProductsPath": {"tool": "create-build-directory", "description": "CreateBuildDirectory /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/BuildProductsPath", "inputs": [], "outputs": ["<CreateBuildDirectory-/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/BuildProductsPath>", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/BuildProductsPath"]}, "P0:::CreateBuildDirectory /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/BuildProductsPath/Release": {"tool": "create-build-directory", "description": "CreateBuildDirectory /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/BuildProductsPath/Release", "inputs": ["/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/BuildProductsPath"], "outputs": ["<CreateBuildDirectory-/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/BuildProductsPath/Release>", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/BuildProductsPath/Release"]}, "P0:::CreateBuildDirectory /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation": {"tool": "create-build-directory", "description": "CreateBuildDirectory /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation", "inputs": [], "outputs": ["<CreateBuildDirectory-/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation>", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation"]}, "P0:::CreateBuildDirectory /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath": {"tool": "create-build-directory", "description": "CreateBuildDirectory /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath", "inputs": [], "outputs": ["<CreateBuildDirectory-/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath>", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath"]}, "P0:::CreateBuildDirectory /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/EagerLinkingTBDs/Release": {"tool": "create-build-directory", "description": "CreateBuildDirectory /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/EagerLinkingTBDs/Release", "inputs": ["/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath"], "outputs": ["<CreateBuildDirectory-/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/EagerLinkingTBDs/Release>", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/EagerLinkingTBDs/Release"]}, "P0:::Gate /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/BuildProductsPath/Release/ClaudeConfigManager.app.dSYM-target-ClaudeConfigManager-****************************************************************-": {"tool": "phony", "inputs": ["/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/BuildProductsPath/Release/ClaudeConfigManager.app.dSYM/Contents/Resources/DWARF/ClaudeConfigManager", "<GenerateDSYMFile /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/BuildProductsPath/Release/ClaudeConfigManager.app.dSYM/Contents/Resources/DWARF/ClaudeConfigManager>", "<target-ClaudeConfigManager-****************************************************************--begin-compiling>"], "outputs": ["/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/BuildProductsPath/Release/ClaudeConfigManager.app.dSYM/"]}, "P0:::Gate WorkspaceHeaderMapVFSFilesWritten": {"tool": "phony", "inputs": ["/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager-dd5074e12287a3259fdfd8e81b104e7a-VFS/all-product-headers.yaml"], "outputs": ["<WorkspaceHeaderMapVFSFilesWritten>"]}, "P0:::Gate target-ClaudeConfigManager-****************************************************************--AppIntentsMetadataTaskProducer": {"tool": "phony", "inputs": ["<target-ClaudeConfigManager-****************************************************************--ModuleVerifierTaskProducer>", "<target-ClaudeConfigManager-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-ClaudeConfigManager-****************************************************************--begin-compiling>", "<ExtractAppIntentsMetadata /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app/Contents/Resources/Metadata.appintents>", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/ClaudeConfigManager.DependencyMetadataFileList", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/ClaudeConfigManager.DependencyStaticMetadataFileList", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager.SwiftConstValuesFileList"], "outputs": ["<target-ClaudeConfigManager-****************************************************************--AppIntentsMetadataTaskProducer>"]}, "P0:::Gate target-ClaudeConfigManager-****************************************************************--Barrier-ChangeAlternatePermissions": {"tool": "phony", "inputs": ["<target-ClaudeConfigManager-****************************************************************--Barrier-ChangePermissions>", "<target-ClaudeConfigManager-****************************************************************--will-sign>", "<target-ClaudeConfigManager-****************************************************************--begin-compiling>"], "outputs": ["<target-ClaudeConfigManager-****************************************************************--Barrier-ChangeAlternatePermissions>"]}, "P0:::Gate target-ClaudeConfigManager-****************************************************************--Barrier-ChangePermissions": {"tool": "phony", "inputs": ["<target-ClaudeConfigManager-****************************************************************--Barrier-StripSymbols>", "<target-ClaudeConfigManager-****************************************************************--will-sign>", "<target-ClaudeConfigManager-****************************************************************--begin-compiling>", "<SetMode /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app>", "<SetOwner /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app>"], "outputs": ["<target-ClaudeConfigManager-****************************************************************--Barrier-ChangePermissions>"]}, "P0:::Gate target-ClaudeConfigManager-****************************************************************--Barrier-CodeSign": {"tool": "phony", "inputs": ["<target-ClaudeConfigManager-****************************************************************--Barrier-ChangeAlternatePermissions>", "<target-ClaudeConfigManager-****************************************************************--will-sign>", "<target-ClaudeConfigManager-****************************************************************--begin-compiling>", "<CodeSign /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app>"], "outputs": ["<target-ClaudeConfigManager-****************************************************************--Barrier-CodeSign>"]}, "P0:::Gate target-ClaudeConfigManager-****************************************************************--Barrier-CopyAside": {"tool": "phony", "inputs": ["<target-ClaudeConfigManager-****************************************************************--Barrier-GenerateStubAPI>", "<target-ClaudeConfigManager-****************************************************************--will-sign>", "<target-ClaudeConfigManager-****************************************************************--begin-compiling>"], "outputs": ["<target-ClaudeConfigManager-****************************************************************--Barrier-CopyAside>"]}, "P0:::Gate target-ClaudeConfigManager-****************************************************************--Barrier-GenerateStubAPI": {"tool": "phony", "inputs": ["<target-ClaudeConfigManager-****************************************************************--ProductPostprocessingTaskProducer>", "<target-ClaudeConfigManager-****************************************************************--begin-compiling>"], "outputs": ["<target-ClaudeConfigManager-****************************************************************--Barrier-GenerateStubAPI>"]}, "P0:::Gate target-ClaudeConfigManager-****************************************************************--Barrier-RegisterExecutionPolicyException": {"tool": "phony", "inputs": ["<target-ClaudeConfigManager-****************************************************************--Barrier-CodeSign>", "<target-ClaudeConfigManager-****************************************************************--will-sign>", "<target-ClaudeConfigManager-****************************************************************--begin-compiling>", "<RegisterExecutionPolicyException /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app>"], "outputs": ["<target-ClaudeConfigManager-****************************************************************--<PERSON>ier-RegisterExecutionPolicyException>"]}, "P0:::Gate target-ClaudeConfigManager-****************************************************************--Barrier-RegisterProduct": {"tool": "phony", "inputs": ["<target-ClaudeConfigManager-****************************************************************--Barrier-Validate>", "<target-ClaudeConfigManager-****************************************************************--will-sign>", "<target-ClaudeConfigManager-****************************************************************--begin-compiling>", "<LSRegisterURL /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app>", "<Touch /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app>"], "outputs": ["<target-ClaudeConfigManager-****************************************************************--Barrier-RegisterProduct>"]}, "P0:::Gate target-ClaudeConfigManager-****************************************************************--Barrier-StripSymbols": {"tool": "phony", "inputs": ["<target-ClaudeConfigManager-****************************************************************--Barrier-CopyAside>", "<target-ClaudeConfigManager-****************************************************************--will-sign>", "<target-ClaudeConfigManager-****************************************************************--begin-compiling>", "<Strip /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app/Contents/MacOS/ClaudeConfigManager>"], "outputs": ["<target-ClaudeConfigManager-****************************************************************--Barrier-StripSymbols>"]}, "P0:::Gate target-ClaudeConfigManager-****************************************************************--Barrier-Validate": {"tool": "phony", "inputs": ["<target-ClaudeConfigManager-****************************************************************--<PERSON>ier-RegisterExecutionPolicyException>", "<target-ClaudeConfigManager-****************************************************************--will-sign>", "<target-ClaudeConfigManager-****************************************************************--begin-compiling>", "<Validate /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app>"], "outputs": ["<target-ClaudeConfigManager-****************************************************************--Barrier-Validate>"]}, "P0:::Gate target-ClaudeConfigManager-****************************************************************--CopySwiftPackageResourcesTaskProducer": {"tool": "phony", "inputs": ["<target-ClaudeConfigManager-****************************************************************--ModuleVerifierTaskProducer>", "<target-ClaudeConfigManager-****************************************************************--begin-compiling>"], "outputs": ["<target-ClaudeConfigManager-****************************************************************--CopySwiftPackageResourcesTaskProducer>"]}, "P0:::Gate target-ClaudeConfigManager-****************************************************************--CustomTaskProducer": {"tool": "phony", "inputs": ["<target-ClaudeConfigManager-****************************************************************--ModuleVerifierTaskProducer>", "<target-ClaudeConfigManager-****************************************************************--begin-compiling>"], "outputs": ["<target-ClaudeConfigManager-****************************************************************--CustomTaskProducer>"]}, "P0:::Gate target-ClaudeConfigManager-****************************************************************--DocumentationTaskProducer": {"tool": "phony", "inputs": ["<target-ClaudeConfigManager-****************************************************************--ModuleVerifierTaskProducer>", "<target-ClaudeConfigManager-****************************************************************--begin-compiling>"], "outputs": ["<target-ClaudeConfigManager-****************************************************************--DocumentationTaskProducer>"]}, "P0:::Gate target-ClaudeConfigManager-****************************************************************--GenerateAppPlaygroundAssetCatalogTaskProducer": {"tool": "phony", "inputs": ["<target-ClaudeConfigManager-****************************************************************--GeneratedFilesTaskProducer>", "<target-ClaudeConfigManager-****************************************************************--begin-compiling>"], "outputs": ["<target-ClaudeConfigManager-****************************************************************--GenerateAppPlaygroundAssetCatalogTaskProducer>"]}, "P0:::Gate target-ClaudeConfigManager-****************************************************************--GeneratedFilesTaskProducer": {"tool": "phony", "inputs": ["<target-ClaudeConfigManager-****************************************************************--ProductStructureTaskProducer>", "<target-ClaudeConfigManager-****************************************************************--begin-compiling>", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/ClaudeConfigManager.app.xcent", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/ClaudeConfigManager.app.xcent.der", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/DerivedSources/Entitlements.plist"], "outputs": ["<target-ClaudeConfigManager-****************************************************************--GeneratedFilesTaskProducer>"]}, "P0:::Gate target-ClaudeConfigManager-****************************************************************--HeadermapTaskProducer": {"tool": "phony", "inputs": ["<target-ClaudeConfigManager-****************************************************************--GenerateAppPlaygroundAssetCatalogTaskProducer>", "<target-ClaudeConfigManager-****************************************************************--begin-compiling>", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/ClaudeConfigManager-all-non-framework-target-headers.hmap", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/ClaudeConfigManager-all-target-headers.hmap", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/ClaudeConfigManager-generated-files.hmap", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/ClaudeConfigManager-own-target-headers.hmap", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/ClaudeConfigManager-project-headers.hmap", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/ClaudeConfigManager.hmap"], "outputs": ["<target-ClaudeConfigManager-****************************************************************--HeadermapTaskProducer>"]}, "P0:::Gate target-ClaudeConfigManager-****************************************************************--InfoPlistTaskProducer": {"tool": "phony", "inputs": ["<target-ClaudeConfigManager-****************************************************************--ModuleVerifierTaskProducer>", "<target-ClaudeConfigManager-****************************************************************--begin-compiling>", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app/Contents/Info.plist", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app/Contents/PkgInfo", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/empty-ClaudeConfigManager.plist"], "outputs": ["<target-ClaudeConfigManager-****************************************************************--InfoPlistTaskProducer>"]}, "P0:::Gate target-ClaudeConfigManager-****************************************************************--ModuleMapTaskProducer": {"tool": "phony", "inputs": ["<target-ClaudeConfigManager-****************************************************************--ModuleVerifierTaskProducer>", "<target-ClaudeConfigManager-****************************************************************--begin-compiling>"], "outputs": ["<target-ClaudeConfigManager-****************************************************************--ModuleMapTaskProducer>"]}, "P0:::Gate target-ClaudeConfigManager-****************************************************************--ModuleVerifierTaskProducer": {"tool": "phony", "inputs": ["<target-ClaudeConfigManager-****************************************************************--RealityAssetsTaskProducer>", "<target-ClaudeConfigManager-****************************************************************--begin-compiling>"], "outputs": ["<target-ClaudeConfigManager-****************************************************************--ModuleVerifierTaskProducer>"]}, "P0:::Gate target-ClaudeConfigManager-****************************************************************--ProductPostprocessingTaskProducer": {"tool": "phony", "inputs": ["<target-ClaudeConfigManager-****************************************************************--ModuleVerifierTaskProducer>", "<target-ClaudeConfigManager-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-ClaudeConfigManager-****************************************************************--ModuleMapTaskProducer>", "<target-ClaudeConfigManager-****************************************************************--SwiftPackageCopyFilesTaskProducer>", "<target-ClaudeConfigManager-****************************************************************--InfoPlistTaskProducer>", "<target-ClaudeConfigManager-****************************************************************--SanitizerTaskProducer>", "<target-ClaudeConfigManager-****************************************************************--SwiftStandardLibrariesTaskProducer>", "<target-ClaudeConfigManager-****************************************************************--SwiftFrameworkABICheckerTaskProducer>", "<target-ClaudeConfigManager-****************************************************************--SwiftABIBaselineGenerationTaskProducer>", "<target-ClaudeConfigManager-****************************************************************--TestTargetTaskProducer>", "<target-ClaudeConfigManager-****************************************************************--TestHostTaskProducer>", "<target-ClaudeConfigManager-****************************************************************--CopySwiftPackageResourcesTaskProducer>", "<target-ClaudeConfigManager-****************************************************************--TAPISymbolExtractorTaskProducer>", "<target-ClaudeConfigManager-****************************************************************--DocumentationTaskProducer>", "<target-ClaudeConfigManager-****************************************************************--CustomTaskProducer>", "<target-ClaudeConfigManager-****************************************************************--StubBinaryTaskProducer>", "<target-ClaudeConfigManager-****************************************************************--AppIntentsMetadataTaskProducer>", "<target-ClaudeConfigManager-****************************************************************--begin-compiling>"], "outputs": ["<target-ClaudeConfigManager-****************************************************************--ProductPostprocessingTaskProducer>"]}, "P0:::Gate target-ClaudeConfigManager-****************************************************************--ProductStructureTaskProducer": {"tool": "phony", "inputs": ["<target-ClaudeConfigManager-****************************************************************--start>", "<target-ClaudeConfigManager-****************************************************************--begin-compiling>", "<MkDir /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app>", "<MkDir /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app/Contents>", "<MkDir /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app/Contents/MacOS>", "<MkDir /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app/Contents/Resources>", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/BuildProductsPath/Release/ClaudeConfigManager.app"], "outputs": ["<target-ClaudeConfigManager-****************************************************************--ProductStructureTaskProducer>"]}, "P0:::Gate target-ClaudeConfigManager-****************************************************************--RealityAssetsTaskProducer": {"tool": "phony", "inputs": ["<target-ClaudeConfigManager-****************************************************************--HeadermapTaskProducer>", "<target-ClaudeConfigManager-****************************************************************--begin-compiling>"], "outputs": ["<target-ClaudeConfigManager-****************************************************************--RealityAssetsTaskProducer>"]}, "P0:::Gate target-ClaudeConfigManager-****************************************************************--SanitizerTaskProducer": {"tool": "phony", "inputs": ["<target-ClaudeConfigManager-****************************************************************--ModuleVerifierTaskProducer>", "<target-ClaudeConfigManager-****************************************************************--begin-compiling>"], "outputs": ["<target-ClaudeConfigManager-****************************************************************--SanitizerTaskProducer>"]}, "P0:::Gate target-ClaudeConfigManager-****************************************************************--StubBinaryTaskProducer": {"tool": "phony", "inputs": ["<target-ClaudeConfigManager-****************************************************************--ModuleVerifierTaskProducer>", "<target-ClaudeConfigManager-****************************************************************--begin-compiling>"], "outputs": ["<target-ClaudeConfigManager-****************************************************************--StubBinaryTaskProducer>"]}, "P0:::Gate target-ClaudeConfigManager-****************************************************************--SwiftABIBaselineGenerationTaskProducer": {"tool": "phony", "inputs": ["<target-ClaudeConfigManager-****************************************************************--ModuleVerifierTaskProducer>", "<target-ClaudeConfigManager-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-ClaudeConfigManager-****************************************************************--begin-compiling>"], "outputs": ["<target-ClaudeConfigManager-****************************************************************--SwiftABIBaselineGenerationTaskProducer>"]}, "P0:::Gate target-ClaudeConfigManager-****************************************************************--SwiftFrameworkABICheckerTaskProducer": {"tool": "phony", "inputs": ["<target-ClaudeConfigManager-****************************************************************--ModuleVerifierTaskProducer>", "<target-ClaudeConfigManager-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-ClaudeConfigManager-****************************************************************--begin-compiling>"], "outputs": ["<target-ClaudeConfigManager-****************************************************************--SwiftFrameworkABICheckerTaskProducer>"]}, "P0:::Gate target-ClaudeConfigManager-****************************************************************--SwiftPackageCopyFilesTaskProducer": {"tool": "phony", "inputs": ["<target-ClaudeConfigManager-****************************************************************--ModuleVerifierTaskProducer>", "<target-ClaudeConfigManager-****************************************************************--begin-compiling>"], "outputs": ["<target-ClaudeConfigManager-****************************************************************--SwiftPackageCopyFilesTaskProducer>"]}, "P0:::Gate target-ClaudeConfigManager-****************************************************************--SwiftStandardLibrariesTaskProducer": {"tool": "phony", "inputs": ["<target-ClaudeConfigManager-****************************************************************--ModuleVerifierTaskProducer>", "<target-ClaudeConfigManager-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-ClaudeConfigManager-****************************************************************--begin-compiling>", "<CopySwiftStdlib /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app>"], "outputs": ["<target-ClaudeConfigManager-****************************************************************--SwiftStandardLibrariesTaskProducer>"]}, "P0:::Gate target-ClaudeConfigManager-****************************************************************--TAPISymbolExtractorTaskProducer": {"tool": "phony", "inputs": ["<target-ClaudeConfigManager-****************************************************************--ModuleVerifierTaskProducer>", "<target-ClaudeConfigManager-****************************************************************--begin-compiling>"], "outputs": ["<target-ClaudeConfigManager-****************************************************************--TAPISymbolExtractorTaskProducer>"]}, "P0:::Gate target-ClaudeConfigManager-****************************************************************--TestHostTaskProducer": {"tool": "phony", "inputs": ["<target-ClaudeConfigManager-****************************************************************--ModuleVerifierTaskProducer>", "<target-ClaudeConfigManager-****************************************************************--begin-compiling>"], "outputs": ["<target-ClaudeConfigManager-****************************************************************--TestHostTaskProducer>"]}, "P0:::Gate target-ClaudeConfigManager-****************************************************************--TestTargetPostprocessingTaskProducer": {"tool": "phony", "inputs": ["<target-ClaudeConfigManager-****************************************************************--ProductPostprocessingTaskProducer>", "<target-ClaudeConfigManager-****************************************************************--begin-compiling>"], "outputs": ["<target-ClaudeConfigManager-****************************************************************--TestTargetPostprocessingTaskProducer>"]}, "P0:::Gate target-ClaudeConfigManager-****************************************************************--TestTargetTaskProducer": {"tool": "phony", "inputs": ["<target-ClaudeConfigManager-****************************************************************--ModuleVerifierTaskProducer>", "<target-ClaudeConfigManager-****************************************************************--begin-compiling>"], "outputs": ["<target-ClaudeConfigManager-****************************************************************--TestTargetTaskProducer>"]}, "P0:::Gate target-ClaudeConfigManager-****************************************************************--copy-headers-completion": {"tool": "phony", "inputs": ["<target-ClaudeConfigManager-****************************************************************--begin-compiling>", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/DerivedSources/GeneratedAssetSymbols.h", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/DerivedSources/GeneratedAssetSymbols-Index.plist"], "outputs": ["<target-ClaudeConfigManager-****************************************************************--copy-headers-completion>"]}, "P0:::Gate target-ClaudeConfigManager-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources": {"tool": "phony", "inputs": ["<target-ClaudeConfigManager-****************************************************************--ModuleVerifierTaskProducer>", "<target-ClaudeConfigManager-****************************************************************--begin-compiling>", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/assetcatalog_output/thinned/", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/assetcatalog_dependencies_thinned", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/assetcatalog_generated_info.plist_thinned", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/assetcatalog_output/unthinned/", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/assetcatalog_dependencies_unthinned", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/assetcatalog_generated_info.plist_unthinned", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/DerivedSources/GeneratedAssetSymbols.h", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/DerivedSources/GeneratedAssetSymbols-Index.plist", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/assetcatalog_generated_info.plist", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app/Contents/Resources/Assets.car", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/assetcatalog_signature", "<MkDir /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/assetcatalog_output/thinned>", "<MkDir /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/assetcatalog_output/unthinned>", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager Swift Compilation Finished", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/BuildProductsPath/Release/ClaudeConfigManager.swiftmodule/x86_64-apple-macos.abi.json", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/BuildProductsPath/Release/ClaudeConfigManager.swiftmodule/x86_64-apple-macos.swiftdoc", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/BuildProductsPath/Release/ClaudeConfigManager.swiftmodule/x86_64-apple-macos.swiftmodule", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager_lto.o", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager_dependency_info.dat", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ContentView.o", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ConfigService.o", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/StatusItemManager.o", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfig.o", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/AppState.o", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/MenuBarView.o", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManagerApp.o", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/AppDelegate.o", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/MenuBarViewModel.o", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/KeychainService.o", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ProcessService.o", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/Logger.o", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/GeneratedAssetSymbols.o", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ContentView.stringsdata", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ConfigService.stringsdata", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/StatusItemManager.stringsdata", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfig.stringsdata", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/AppState.stringsdata", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/MenuBarView.stringsdata", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManagerApp.stringsdata", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/AppDelegate.stringsdata", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/MenuBarViewModel.stringsdata", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/KeychainService.stringsdata", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ProcessService.stringsdata", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/Logger.stringsdata", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager-master.swiftconstvalues", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager.swiftmodule", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager.swiftsourceinfo", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager.abi.json", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager-Swift.h", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager.swiftdoc", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager-OutputFileMap.json", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager.LinkFileList", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager.SwiftFileList", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager_const_extract_protocols.json"], "outputs": ["<target-ClaudeConfigManager-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>"]}, "P0:::Gate target-ClaudeConfigManager-****************************************************************--generated-headers": {"tool": "phony", "inputs": ["<target-ClaudeConfigManager-****************************************************************--begin-compiling>", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/DerivedSources/GeneratedAssetSymbols.h", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/DerivedSources/GeneratedAssetSymbols-Index.plist"], "outputs": ["<target-ClaudeConfigManager-****************************************************************--generated-headers>"]}, "P0:::Gate target-ClaudeConfigManager-****************************************************************--swift-generated-headers": {"tool": "phony", "inputs": ["<target-ClaudeConfigManager-****************************************************************--begin-compiling>", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ContentView.o", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ConfigService.o", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/StatusItemManager.o", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfig.o", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/AppState.o", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/MenuBarView.o", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManagerApp.o", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/AppDelegate.o", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/MenuBarViewModel.o", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/KeychainService.o", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ProcessService.o", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/Logger.o", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/GeneratedAssetSymbols.o", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ContentView.stringsdata", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ConfigService.stringsdata", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/StatusItemManager.stringsdata", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfig.stringsdata", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/AppState.stringsdata", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/MenuBarView.stringsdata", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManagerApp.stringsdata", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/AppDelegate.stringsdata", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/MenuBarViewModel.stringsdata", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/KeychainService.stringsdata", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ProcessService.stringsdata", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/Logger.stringsdata", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager-master.swiftconstvalues", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager.swiftmodule", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager.swiftsourceinfo", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager.abi.json", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager-Swift.h", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager.swiftdoc", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/DerivedSources/ClaudeConfigManager-Swift.h"], "outputs": ["<target-ClaudeConfigManager-****************************************************************--swift-generated-headers>"]}, "P0:target-ClaudeConfigManager-****************************************************************-:Release:CodeSign /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app": {"tool": "code-sign-task", "description": "CodeSign /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app", "inputs": ["/Users/<USER>/XcodeProjects/ClaudeConfigManager/ClaudeConfigManager/App/AppDelegate.swift/", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/ClaudeConfigManager/App/AppState.swift/", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/ClaudeConfigManager/App/ClaudeConfigManagerApp.swift/", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/ClaudeConfigManager/Assets.xcassets/", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/ClaudeConfigManager/Core/Models/ClaudeConfig.swift/", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/ClaudeConfigManager/Core/Services/ConfigService.swift/", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/ClaudeConfigManager/Core/Services/KeychainService.swift/", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/ClaudeConfigManager/Core/Services/Logger.swift/", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/ClaudeConfigManager/Core/Services/ProcessService.swift/", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/ClaudeConfigManager/Features/ContentView.swift/", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/ClaudeConfigManager/Features/MenuBar/MenuBarView.swift/", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/ClaudeConfigManager/Features/MenuBar/MenuBarViewModel.swift/", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/ClaudeConfigManager/Features/MenuBar/StatusItemManager.swift/", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/ClaudeConfigManager/Preview Content/Preview Assets.xcassets/", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app/Contents/Info.plist/", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/ClaudeConfigManager.app.xcent/", "<target-ClaudeConfigManager-****************************************************************--Barrier-ChangeAlternatePermissions>", "<target-ClaudeConfigManager-****************************************************************--will-sign>", "<target-ClaudeConfigManager-****************************************************************--entry>", "<TRIGGER: SetMode u+w,go-w,a+rX /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app>", "<TRIGGER: Strip /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app/Contents/MacOS/ClaudeConfigManager>"], "outputs": ["/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app/_CodeSignature", "<CodeSign /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app>", "<TRIGGER: CodeSign /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app>"]}, "P0:target-ClaudeConfigManager-****************************************************************-:Release:CompileAssetCatalogVariant thinned /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app/Contents/Resources /Users/<USER>/XcodeProjects/ClaudeConfigManager/ClaudeConfigManager/Assets.xcassets": {"tool": "shell", "description": "CompileAssetCatalogVariant thinned /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app/Contents/Resources /Users/<USER>/XcodeProjects/ClaudeConfigManager/ClaudeConfigManager/Assets.xcassets", "inputs": ["/Users/<USER>/XcodeProjects/ClaudeConfigManager/ClaudeConfigManager/Assets.xcassets/", "<MkDir /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/assetcatalog_output/thinned>", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/assetcatalog_output/thinned", "<target-ClaudeConfigManager-****************************************************************--ModuleVerifierTaskProducer>", "<target-ClaudeConfigManager-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/assetcatalog_output/thinned/", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/assetcatalog_dependencies_thinned", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/assetcatalog_generated_info.plist_thinned"], "args": ["/Applications/Xcode.app/Contents/Developer/usr/bin/actool", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/ClaudeConfigManager/Assets.xcassets", "--compile", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/assetcatalog_output/thinned", "--output-format", "human-readable-text", "--notices", "--warnings", "--export-dependency-info", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/assetcatalog_dependencies_thinned", "--output-partial-info-plist", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/assetcatalog_generated_info.plist_thinned", "--app-icon", "AppIcon", "--accent-color", "AccentColor", "--enable-on-demand-resources", "NO", "--development-region", "en", "--target-device", "mac", "--minimum-deployment-target", "15.0", "--platform", "macosx"], "env": {}, "working-directory": "/Users/<USER>/XcodeProjects/ClaudeConfigManager", "control-enabled": false, "deps": ["/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/assetcatalog_dependencies_thinned"], "deps-style": "dependency-info", "signature": "3f159fd44d1d514f4ccbeade97d340e9"}, "P0:target-ClaudeConfigManager-****************************************************************-:Release:CompileAssetCatalogVariant unthinned /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app/Contents/Resources /Users/<USER>/XcodeProjects/ClaudeConfigManager/ClaudeConfigManager/Assets.xcassets": {"tool": "shell", "description": "CompileAssetCatalogVariant unthinned /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app/Contents/Resources /Users/<USER>/XcodeProjects/ClaudeConfigManager/ClaudeConfigManager/Assets.xcassets", "inputs": ["/Users/<USER>/XcodeProjects/ClaudeConfigManager/ClaudeConfigManager/Assets.xcassets/", "<MkDir /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/assetcatalog_output/unthinned>", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/assetcatalog_output/unthinned", "<target-ClaudeConfigManager-****************************************************************--ModuleVerifierTaskProducer>", "<target-ClaudeConfigManager-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/assetcatalog_output/unthinned/", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/assetcatalog_dependencies_unthinned", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/assetcatalog_generated_info.plist_unthinned"], "args": ["/Applications/Xcode.app/Contents/Developer/usr/bin/actool", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/ClaudeConfigManager/Assets.xcassets", "--compile", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/assetcatalog_output/unthinned", "--output-format", "human-readable-text", "--notices", "--warnings", "--export-dependency-info", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/assetcatalog_dependencies_unthinned", "--output-partial-info-plist", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/assetcatalog_generated_info.plist_unthinned", "--app-icon", "AppIcon", "--accent-color", "AccentColor", "--enable-on-demand-resources", "NO", "--development-region", "en", "--target-device", "mac", "--minimum-deployment-target", "15.0", "--platform", "macosx"], "env": {}, "working-directory": "/Users/<USER>/XcodeProjects/ClaudeConfigManager", "control-enabled": false, "deps": ["/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/assetcatalog_dependencies_unthinned"], "deps-style": "dependency-info", "signature": "dc5e33896ce721afe5cc593837523b8e"}, "P0:target-ClaudeConfigManager-****************************************************************-:Release:CopySwiftLibs /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app": {"tool": "embed-swift-stdlib", "description": "CopySwiftLibs /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app", "inputs": ["/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app/Contents/MacOS/ClaudeConfigManager", "<target-ClaudeConfigManager-****************************************************************--ModuleVerifierTaskProducer>", "<target-ClaudeConfigManager-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-ClaudeConfigManager-****************************************************************--immediate>"], "outputs": ["<CopySwiftStdlib /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app>"], "deps": "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/SwiftStdLibToolInputDependencies.dep"}, "P0:target-ClaudeConfigManager-****************************************************************-:Release:ExtractAppIntentsMetadata": {"tool": "shell", "description": "ExtractAppIntentsMetadata", "inputs": ["/Users/<USER>/XcodeProjects/ClaudeConfigManager/ClaudeConfigManager/Features/ContentView.swift", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/ClaudeConfigManager/Core/Services/ConfigService.swift", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/ClaudeConfigManager/Features/MenuBar/StatusItemManager.swift", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/ClaudeConfigManager/Core/Models/ClaudeConfig.swift", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/ClaudeConfigManager/App/AppState.swift", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/ClaudeConfigManager/Features/MenuBar/MenuBarView.swift", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/ClaudeConfigManager/App/ClaudeConfigManagerApp.swift", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/ClaudeConfigManager/App/AppDelegate.swift", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/ClaudeConfigManager/Features/MenuBar/MenuBarViewModel.swift", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/ClaudeConfigManager/Core/Services/KeychainService.swift", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/ClaudeConfigManager/Core/Services/ProcessService.swift", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/ClaudeConfigManager/Core/Services/Logger.swift", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager-master.swiftconstvalues", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app/Contents/MacOS/ClaudeConfigManager", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/ClaudeConfigManager.DependencyMetadataFileList", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/ClaudeConfigManager.DependencyStaticMetadataFileList", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager_dependency_info.dat", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager.SwiftFileList", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager.SwiftConstValuesFileList", "<target-ClaudeConfigManager-****************************************************************--ModuleVerifierTaskProducer>", "<target-ClaudeConfigManager-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-ClaudeConfigManager-****************************************************************--entry>"], "outputs": ["/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ExtractedAppShortcutsMetadata.stringsdata", "<ExtractAppIntentsMetadata /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app/Contents/Resources/Metadata.appintents>"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/appintentsmetadataprocessor", "--toolchain-dir", "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain", "--module-name", "ClaudeConfigManager", "--sdk-root", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "--xcode-version", "16F6", "--platform-family", "macOS", "--deployment-target", "15.0", "--bundle-identifier", "com.claude.configmanager", "--output", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app/Contents/Resources", "--target-triple", "x86_64-apple-macos15.0", "--binary-file", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app/Contents/MacOS/ClaudeConfigManager", "--dependency-file", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager_dependency_info.dat", "--stringsdata-file", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ExtractedAppShortcutsMetadata.stringsdata", "--source-file-list", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager.SwiftFileList", "--metadata-file-list", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/ClaudeConfigManager.DependencyMetadataFileList", "--static-metadata-file-list", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/ClaudeConfigManager.DependencyStaticMetadataFileList", "--swift-const-vals-list", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager.SwiftConstValuesFileList", "--compile-time-extraction", "--deployment-aware-processing", "--validate-assistant-intents", "--no-app-shortcuts-localization"], "env": {}, "working-directory": "/Users/<USER>/XcodeProjects/ClaudeConfigManager", "signature": "372fd976410f33efb0ebd815b1f3980a"}, "P0:target-ClaudeConfigManager-****************************************************************-:Release:Gate target-ClaudeConfigManager-****************************************************************--begin-compiling": {"tool": "phony", "inputs": ["<target-ClaudeConfigManager-****************************************************************-Release-macosx--x86_64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation>", "<CreateBuildDirectory-/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath>", "<CreateBuildDirectory-/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/BuildProductsPath>", "<CreateBuildDirectory-/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/BuildProductsPath/Release>", "<CreateBuildDirectory-/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/EagerLinkingTBDs/Release>"], "outputs": ["<target-ClaudeConfigManager-****************************************************************--begin-compiling>"]}, "P0:target-ClaudeConfigManager-****************************************************************-:Release:Gate target-ClaudeConfigManager-****************************************************************--begin-linking": {"tool": "phony", "inputs": ["<target-ClaudeConfigManager-****************************************************************-Release-macosx--x86_64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation>", "<CreateBuildDirectory-/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath>", "<CreateBuildDirectory-/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/BuildProductsPath>", "<CreateBuildDirectory-/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/BuildProductsPath/Release>", "<CreateBuildDirectory-/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/EagerLinkingTBDs/Release>"], "outputs": ["<target-ClaudeConfigManager-****************************************************************--begin-linking>"]}, "P0:target-ClaudeConfigManager-****************************************************************-:Release:Gate target-ClaudeConfigManager-****************************************************************--begin-scanning": {"tool": "phony", "inputs": ["<target-ClaudeConfigManager-****************************************************************-Release-macosx--x86_64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation>", "<CreateBuildDirectory-/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath>", "<CreateBuildDirectory-/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/BuildProductsPath>", "<CreateBuildDirectory-/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/BuildProductsPath/Release>", "<CreateBuildDirectory-/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/EagerLinkingTBDs/Release>", "<target-ClaudeConfigManager-****************************************************************--begin-compiling>"], "outputs": ["<target-ClaudeConfigManager-****************************************************************--begin-scanning>"]}, "P0:target-ClaudeConfigManager-****************************************************************-:Release:Gate target-ClaudeConfigManager-****************************************************************--end": {"tool": "phony", "inputs": ["<target-ClaudeConfigManager-****************************************************************--entry>", "<CodeSign /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app>", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/assetcatalog_output/thinned/", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/assetcatalog_dependencies_thinned", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/assetcatalog_generated_info.plist_thinned", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/assetcatalog_output/unthinned/", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/assetcatalog_dependencies_unthinned", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/assetcatalog_generated_info.plist_unthinned", "<CopySwiftStdlib /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app>", "<ExtractAppIntentsMetadata /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app/Contents/Resources/Metadata.appintents>", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/DerivedSources/GeneratedAssetSymbols.h", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/DerivedSources/GeneratedAssetSymbols-Index.plist", "<GenerateDSYMFile /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/BuildProductsPath/Release/ClaudeConfigManager.app.dSYM/Contents/Resources/DWARF/ClaudeConfigManager>", "<GenerateDSYMFile /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/BuildProductsPath/Release/ClaudeConfigManager.app.dSYM/Contents/Resources/DWARF/ClaudeConfigManager>", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/assetcatalog_generated_info.plist", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app/Contents/Resources/Assets.car", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/assetcatalog_signature", "<MkDir /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app>", "<MkDir /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app/Contents>", "<MkDir /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app/Contents/MacOS>", "<MkDir /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app/Contents/Resources>", "<MkDir /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/assetcatalog_output/thinned>", "<MkDir /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/assetcatalog_output/unthinned>", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app/Contents/Info.plist", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app/Contents/PkgInfo", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/ClaudeConfigManager.app.xcent", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/ClaudeConfigManager.app.xcent.der", "<RegisterExecutionPolicyException /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app>", "<LSRegisterURL /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app>", "<SetMode /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app>", "<SetOwner /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app>", "<Strip /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app/Contents/MacOS/ClaudeConfigManager>", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager Swift Compilation Finished", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/BuildProductsPath/Release/ClaudeConfigManager.app", "<Touch /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app>", "<Validate /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app>", "<ValidateDevelopmentAssets-/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build>", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/BuildProductsPath/Release/ClaudeConfigManager.swiftmodule/x86_64-apple-macos.abi.json", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/BuildProductsPath/Release/ClaudeConfigManager.swiftmodule/x86_64-apple-macos.swiftdoc", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/BuildProductsPath/Release/ClaudeConfigManager.swiftmodule/x86_64-apple-macos.swiftmodule", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager_lto.o", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager_dependency_info.dat", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ContentView.o", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ConfigService.o", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/StatusItemManager.o", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfig.o", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/AppState.o", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/MenuBarView.o", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManagerApp.o", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/AppDelegate.o", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/MenuBarViewModel.o", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/KeychainService.o", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ProcessService.o", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/Logger.o", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/GeneratedAssetSymbols.o", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ContentView.stringsdata", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ConfigService.stringsdata", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/StatusItemManager.stringsdata", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfig.stringsdata", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/AppState.stringsdata", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/MenuBarView.stringsdata", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManagerApp.stringsdata", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/AppDelegate.stringsdata", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/MenuBarViewModel.stringsdata", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/KeychainService.stringsdata", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ProcessService.stringsdata", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/Logger.stringsdata", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager-master.swiftconstvalues", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager.swiftmodule", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager.swiftsourceinfo", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager.abi.json", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager-Swift.h", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager.swiftdoc", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/DerivedSources/ClaudeConfigManager-Swift.h", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/DerivedSources/ClaudeConfigManager-Swift.h", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/ClaudeConfigManager-all-non-framework-target-headers.hmap", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/ClaudeConfigManager-all-target-headers.hmap", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/ClaudeConfigManager-generated-files.hmap", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/ClaudeConfigManager-own-target-headers.hmap", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/ClaudeConfigManager-project-headers.hmap", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/ClaudeConfigManager.DependencyMetadataFileList", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/ClaudeConfigManager.DependencyStaticMetadataFileList", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/ClaudeConfigManager.hmap", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/DerivedSources/Entitlements.plist", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager-OutputFileMap.json", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager.LinkFileList", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager.SwiftConstValuesFileList", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager.SwiftFileList", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager_const_extract_protocols.json", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/empty-ClaudeConfigManager.plist", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/BuildProductsPath/Release/ClaudeConfigManager.app.dSYM/", "<target-ClaudeConfigManager-****************************************************************--AppIntentsMetadataTaskProducer>", "<target-ClaudeConfigManager-****************************************************************--Barrier-ChangeAlternatePermissions>", "<target-ClaudeConfigManager-****************************************************************--Barrier-ChangePermissions>", "<target-ClaudeConfigManager-****************************************************************--Barrier-CodeSign>", "<target-ClaudeConfigManager-****************************************************************--Barrier-CopyAside>", "<target-ClaudeConfigManager-****************************************************************--Barrier-GenerateStubAPI>", "<target-ClaudeConfigManager-****************************************************************--<PERSON>ier-RegisterExecutionPolicyException>", "<target-ClaudeConfigManager-****************************************************************--Barrier-RegisterProduct>", "<target-ClaudeConfigManager-****************************************************************--Barrier-StripSymbols>", "<target-ClaudeConfigManager-****************************************************************--Barrier-Validate>", "<target-ClaudeConfigManager-****************************************************************--CopySwiftPackageResourcesTaskProducer>", "<target-ClaudeConfigManager-****************************************************************--CustomTaskProducer>", "<target-ClaudeConfigManager-****************************************************************--DocumentationTaskProducer>", "<target-ClaudeConfigManager-****************************************************************--GenerateAppPlaygroundAssetCatalogTaskProducer>", "<target-ClaudeConfigManager-****************************************************************--GeneratedFilesTaskProducer>", "<target-ClaudeConfigManager-****************************************************************--HeadermapTaskProducer>", "<target-ClaudeConfigManager-****************************************************************--InfoPlistTaskProducer>", "<target-ClaudeConfigManager-****************************************************************--ModuleMapTaskProducer>", "<target-ClaudeConfigManager-****************************************************************--ModuleVerifierTaskProducer>", "<target-ClaudeConfigManager-****************************************************************--ProductPostprocessingTaskProducer>", "<target-ClaudeConfigManager-****************************************************************--ProductStructureTaskProducer>", "<target-ClaudeConfigManager-****************************************************************--RealityAssetsTaskProducer>", "<target-ClaudeConfigManager-****************************************************************--SanitizerTaskProducer>", "<target-ClaudeConfigManager-****************************************************************--StubBinaryTaskProducer>", "<target-ClaudeConfigManager-****************************************************************--SwiftABIBaselineGenerationTaskProducer>", "<target-ClaudeConfigManager-****************************************************************--SwiftFrameworkABICheckerTaskProducer>", "<target-ClaudeConfigManager-****************************************************************--SwiftPackageCopyFilesTaskProducer>", "<target-ClaudeConfigManager-****************************************************************--SwiftStandardLibrariesTaskProducer>", "<target-ClaudeConfigManager-****************************************************************--TAPISymbolExtractorTaskProducer>", "<target-ClaudeConfigManager-****************************************************************--TestHostTaskProducer>", "<target-ClaudeConfigManager-****************************************************************--TestTargetPostprocessingTaskProducer>", "<target-ClaudeConfigManager-****************************************************************--TestTargetTaskProducer>", "<target-ClaudeConfigManager-****************************************************************--copy-headers-completion>", "<target-ClaudeConfigManager-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-ClaudeConfigManager-****************************************************************--generated-headers>", "<target-ClaudeConfigManager-****************************************************************--swift-generated-headers>"], "outputs": ["<target-ClaudeConfigManager-****************************************************************--end>"]}, "P0:target-ClaudeConfigManager-****************************************************************-:Release:Gate target-ClaudeConfigManager-****************************************************************--entry": {"tool": "phony", "inputs": ["<target-ClaudeConfigManager-****************************************************************-Release-macosx--x86_64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation>", "<CreateBuildDirectory-/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath>", "<CreateBuildDirectory-/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/BuildProductsPath>", "<CreateBuildDirectory-/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/BuildProductsPath/Release>", "<CreateBuildDirectory-/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/EagerLinkingTBDs/Release>", "<target-ClaudeConfigManager-****************************************************************--begin-compiling>"], "outputs": ["<target-ClaudeConfigManager-****************************************************************--entry>"]}, "P0:target-ClaudeConfigManager-****************************************************************-:Release:Gate target-ClaudeConfigManager-****************************************************************--immediate": {"tool": "phony", "inputs": ["<target-ClaudeConfigManager-****************************************************************-Release-macosx--x86_64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation>", "<CreateBuildDirectory-/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath>", "<CreateBuildDirectory-/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/BuildProductsPath>", "<CreateBuildDirectory-/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/BuildProductsPath/Release>", "<CreateBuildDirectory-/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/EagerLinkingTBDs/Release>"], "outputs": ["<target-ClaudeConfigManager-****************************************************************--immediate>"]}, "P0:target-ClaudeConfigManager-****************************************************************-:Release:Gate target-ClaudeConfigManager-****************************************************************--linker-inputs-ready": {"tool": "phony", "inputs": ["<target-ClaudeConfigManager-****************************************************************--begin-compiling>", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager_lto.o", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager_dependency_info.dat", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ContentView.o", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ConfigService.o", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/StatusItemManager.o", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfig.o", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/AppState.o", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/MenuBarView.o", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManagerApp.o", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/AppDelegate.o", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/MenuBarViewModel.o", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/KeychainService.o", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ProcessService.o", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/Logger.o", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/GeneratedAssetSymbols.o", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ContentView.stringsdata", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ConfigService.stringsdata", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/StatusItemManager.stringsdata", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfig.stringsdata", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/AppState.stringsdata", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/MenuBarView.stringsdata", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManagerApp.stringsdata", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/AppDelegate.stringsdata", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/MenuBarViewModel.stringsdata", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/KeychainService.stringsdata", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ProcessService.stringsdata", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/Logger.stringsdata", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager-master.swiftconstvalues", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager.swiftmodule", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager.swiftsourceinfo", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager.abi.json", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager-Swift.h", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager.swiftdoc", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager.LinkFileList"], "outputs": ["<target-ClaudeConfigManager-****************************************************************--linker-inputs-ready>"]}, "P0:target-ClaudeConfigManager-****************************************************************-:Release:Gate target-ClaudeConfigManager-****************************************************************--modules-ready": {"tool": "phony", "inputs": ["<target-ClaudeConfigManager-****************************************************************--begin-compiling>", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/BuildProductsPath/Release/ClaudeConfigManager.swiftmodule/x86_64-apple-macos.abi.json", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/BuildProductsPath/Release/ClaudeConfigManager.swiftmodule/x86_64-apple-macos.swiftdoc", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/BuildProductsPath/Release/ClaudeConfigManager.swiftmodule/x86_64-apple-macos.swiftmodule", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ContentView.o", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ConfigService.o", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/StatusItemManager.o", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfig.o", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/AppState.o", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/MenuBarView.o", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManagerApp.o", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/AppDelegate.o", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/MenuBarViewModel.o", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/KeychainService.o", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ProcessService.o", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/Logger.o", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/GeneratedAssetSymbols.o", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ContentView.stringsdata", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ConfigService.stringsdata", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/StatusItemManager.stringsdata", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfig.stringsdata", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/AppState.stringsdata", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/MenuBarView.stringsdata", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManagerApp.stringsdata", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/AppDelegate.stringsdata", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/MenuBarViewModel.stringsdata", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/KeychainService.stringsdata", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ProcessService.stringsdata", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/Logger.stringsdata", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager-master.swiftconstvalues", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager.swiftmodule", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager.swiftsourceinfo", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager.abi.json", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager-Swift.h", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager.swiftdoc", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/DerivedSources/ClaudeConfigManager-Swift.h"], "outputs": ["<target-ClaudeConfigManager-****************************************************************--modules-ready>"]}, "P0:target-ClaudeConfigManager-****************************************************************-:Release:Gate target-ClaudeConfigManager-****************************************************************--unsigned-product-ready": {"tool": "phony", "inputs": ["<target-ClaudeConfigManager-****************************************************************--begin-compiling>", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/assetcatalog_output/thinned/", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/assetcatalog_dependencies_thinned", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/assetcatalog_generated_info.plist_thinned", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/assetcatalog_output/unthinned/", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/assetcatalog_dependencies_unthinned", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/assetcatalog_generated_info.plist_unthinned", "<CopySwiftStdlib /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app>", "<ExtractAppIntentsMetadata /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app/Contents/Resources/Metadata.appintents>", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/DerivedSources/GeneratedAssetSymbols.h", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/DerivedSources/GeneratedAssetSymbols-Index.plist", "<GenerateDSYMFile /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/BuildProductsPath/Release/ClaudeConfigManager.app.dSYM/Contents/Resources/DWARF/ClaudeConfigManager>", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/assetcatalog_generated_info.plist", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app/Contents/Resources/Assets.car", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/assetcatalog_signature", "<MkDir /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/assetcatalog_output/thinned>", "<MkDir /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/assetcatalog_output/unthinned>", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/ClaudeConfigManager.app.xcent", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/ClaudeConfigManager.app.xcent.der", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager Swift Compilation Finished", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/BuildProductsPath/Release/ClaudeConfigManager.swiftmodule/x86_64-apple-macos.abi.json", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/BuildProductsPath/Release/ClaudeConfigManager.swiftmodule/x86_64-apple-macos.swiftdoc", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/BuildProductsPath/Release/ClaudeConfigManager.swiftmodule/x86_64-apple-macos.swiftmodule", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager_lto.o", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager_dependency_info.dat", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ContentView.o", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ConfigService.o", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/StatusItemManager.o", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfig.o", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/AppState.o", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/MenuBarView.o", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManagerApp.o", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/AppDelegate.o", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/MenuBarViewModel.o", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/KeychainService.o", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ProcessService.o", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/Logger.o", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/GeneratedAssetSymbols.o", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ContentView.stringsdata", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ConfigService.stringsdata", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/StatusItemManager.stringsdata", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfig.stringsdata", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/AppState.stringsdata", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/MenuBarView.stringsdata", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManagerApp.stringsdata", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/AppDelegate.stringsdata", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/MenuBarViewModel.stringsdata", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/KeychainService.stringsdata", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ProcessService.stringsdata", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/Logger.stringsdata", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager-master.swiftconstvalues", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager.swiftmodule", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager.swiftsourceinfo", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager.abi.json", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager-Swift.h", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager.swiftdoc", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/DerivedSources/ClaudeConfigManager-Swift.h", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/ClaudeConfigManager.DependencyMetadataFileList", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/ClaudeConfigManager.DependencyStaticMetadataFileList", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/DerivedSources/Entitlements.plist", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager-OutputFileMap.json", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager.LinkFileList", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager.SwiftConstValuesFileList", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager.SwiftFileList", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager_const_extract_protocols.json", "<target-ClaudeConfigManager-****************************************************************--Barrier-GenerateStubAPI>"], "outputs": ["<target-ClaudeConfigManager-****************************************************************--unsigned-product-ready>"]}, "P0:target-ClaudeConfigManager-****************************************************************-:Release:Gate target-ClaudeConfigManager-****************************************************************--will-sign": {"tool": "phony", "inputs": ["<target-ClaudeConfigManager-****************************************************************--unsigned-product-ready>"], "outputs": ["<target-ClaudeConfigManager-****************************************************************--will-sign>"]}, "P0:target-ClaudeConfigManager-****************************************************************-:Release:GenerateAssetSymbols /Users/<USER>/XcodeProjects/ClaudeConfigManager/ClaudeConfigManager/Assets.xcassets": {"tool": "shell", "description": "GenerateAssetSymbols /Users/<USER>/XcodeProjects/ClaudeConfigManager/ClaudeConfigManager/Assets.xcassets", "inputs": ["/Users/<USER>/XcodeProjects/ClaudeConfigManager/ClaudeConfigManager/Assets.xcassets/", "<target-ClaudeConfigManager-****************************************************************--ModuleVerifierTaskProducer>", "<target-ClaudeConfigManager-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/DerivedSources/GeneratedAssetSymbols.h", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/DerivedSources/GeneratedAssetSymbols-Index.plist"], "args": ["/Applications/Xcode.app/Contents/Developer/usr/bin/actool", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/ClaudeConfigManager/Assets.xcassets", "--compile", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app/Contents/Resources", "--output-format", "human-readable-text", "--notices", "--warnings", "--export-dependency-info", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/assetcatalog_dependencies", "--output-partial-info-plist", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/assetcatalog_generated_info.plist", "--app-icon", "AppIcon", "--accent-color", "AccentColor", "--enable-on-demand-resources", "NO", "--development-region", "en", "--target-device", "mac", "--minimum-deployment-target", "15.0", "--platform", "macosx", "--bundle-identifier", "com.claude.configmanager", "--generate-swift-asset-symbol-extensions", "NO", "--generate-swift-asset-symbols", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/DerivedSources/GeneratedAssetSymbols.swift", "--generate-objc-asset-symbols", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/DerivedSources/GeneratedAssetSymbols.h", "--generate-asset-symbol-index", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/DerivedSources/GeneratedAssetSymbols-Index.plist"], "env": {}, "working-directory": "/Users/<USER>/XcodeProjects/ClaudeConfigManager", "control-enabled": false, "signature": "63f8df6f0bc01e35fad04c8a0b24b041"}, "P0:target-ClaudeConfigManager-****************************************************************-:Release:GenerateDSYMFile /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/BuildProductsPath/Release/ClaudeConfigManager.app.dSYM /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app/Contents/MacOS/ClaudeConfigManager": {"tool": "shell", "description": "GenerateDSYMFile /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/BuildProductsPath/Release/ClaudeConfigManager.app.dSYM /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app/Contents/MacOS/ClaudeConfigManager", "inputs": ["/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app/Contents/MacOS/ClaudeConfigManager", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app/Contents/Info.plist", "<Linked Binary /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app/Contents/MacOS/ClaudeConfigManager>", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager.swiftmodule", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/BuildProductsPath/Release/ClaudeConfigManager.swiftmodule/x86_64-apple-macos.swiftmodule", "<target-ClaudeConfigManager-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/BuildProductsPath/Release/ClaudeConfigManager.app.dSYM/Contents/Resources/DWARF/ClaudeConfigManager", "<GenerateDSYMFile /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/BuildProductsPath/Release/ClaudeConfigManager.app.dSYM/Contents/Resources/DWARF/ClaudeConfigManager>"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/dsymutil", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app/Contents/MacOS/ClaudeConfigManager", "-o", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/BuildProductsPath/Release/ClaudeConfigManager.app.dSYM"], "env": {}, "working-directory": "/Users/<USER>/XcodeProjects/ClaudeConfigManager", "signature": "3351b122565cb81c80adeb9803d5579c"}, "P0:target-ClaudeConfigManager-****************************************************************-:Release:LinkAssetCatalog /Users/<USER>/XcodeProjects/ClaudeConfigManager/ClaudeConfigManager/Assets.xcassets": {"tool": "link-assetcatalog", "description": "LinkAssetCatalog /Users/<USER>/XcodeProjects/ClaudeConfigManager/ClaudeConfigManager/Assets.xcassets", "inputs": ["/Users/<USER>/XcodeProjects/ClaudeConfigManager/ClaudeConfigManager/Assets.xcassets/", "<MkDir /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app/Contents/Resources>", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app/Contents/Resources", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/assetcatalog_output/thinned/", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/assetcatalog_output/unthinned/", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/assetcatalog_signature", "<target-ClaudeConfigManager-****************************************************************--ModuleVerifierTaskProducer>", "<target-ClaudeConfigManager-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/assetcatalog_generated_info.plist", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app/Contents/Resources/Assets.car"], "deps": "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/assetcatalog_dependencies"}, "P0:target-ClaudeConfigManager-****************************************************************-:Release:LinkAssetCatalogSignature": {"tool": "link-assetcatalog", "description": "LinkAssetCatalogSignature", "inputs": ["<target-ClaudeConfigManager-****************************************************************--ModuleVerifierTaskProducer>", "<target-ClaudeConfigManager-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/assetcatalog_signature"], "always-out-of-date": true}, "P0:target-ClaudeConfigManager-****************************************************************-:Release:MkDir /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app": {"tool": "mkdir", "description": "MkDir /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app", "inputs": ["<target-ClaudeConfigManager-****************************************************************--start>", "<target-ClaudeConfigManager-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app", "<MkDir /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app>", "<TRIGGER: MkDir /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app>"]}, "P0:target-ClaudeConfigManager-****************************************************************-:Release:MkDir /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app/Contents": {"tool": "mkdir", "description": "MkDir /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app/Contents", "inputs": ["<target-ClaudeConfigManager-****************************************************************--start>", "<target-ClaudeConfigManager-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app/Contents", "<MkDir /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app/Contents>"]}, "P0:target-ClaudeConfigManager-****************************************************************-:Release:MkDir /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app/Contents/MacOS": {"tool": "mkdir", "description": "MkDir /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app/Contents/MacOS", "inputs": ["<target-ClaudeConfigManager-****************************************************************--start>", "<target-ClaudeConfigManager-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app/Contents/MacOS", "<MkDir /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app/Contents/MacOS>"]}, "P0:target-ClaudeConfigManager-****************************************************************-:Release:MkDir /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app/Contents/Resources": {"tool": "mkdir", "description": "MkDir /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app/Contents/Resources", "inputs": ["<target-ClaudeConfigManager-****************************************************************--start>", "<target-ClaudeConfigManager-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app/Contents/Resources", "<MkDir /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app/Contents/Resources>"]}, "P0:target-ClaudeConfigManager-****************************************************************-:Release:MkDir /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/assetcatalog_output/thinned": {"tool": "mkdir", "description": "MkDir /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/assetcatalog_output/thinned", "inputs": ["<target-ClaudeConfigManager-****************************************************************--ModuleVerifierTaskProducer>", "<target-ClaudeConfigManager-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/assetcatalog_output/thinned", "<MkDir /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/assetcatalog_output/thinned>"]}, "P0:target-ClaudeConfigManager-****************************************************************-:Release:MkDir /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/assetcatalog_output/unthinned": {"tool": "mkdir", "description": "MkDir /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/assetcatalog_output/unthinned", "inputs": ["<target-ClaudeConfigManager-****************************************************************--ModuleVerifierTaskProducer>", "<target-ClaudeConfigManager-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/assetcatalog_output/unthinned", "<MkDir /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/assetcatalog_output/unthinned>"]}, "P0:target-ClaudeConfigManager-****************************************************************-:Release:ProcessInfoPlistFile /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app/Contents/Info.plist /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/empty-ClaudeConfigManager.plist": {"tool": "info-plist-processor", "description": "ProcessInfoPlistFile /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app/Contents/Info.plist /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/empty-ClaudeConfigManager.plist", "inputs": ["/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/empty-ClaudeConfigManager.plist", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/assetcatalog_generated_info.plist", "<target-ClaudeConfigManager-****************************************************************--ModuleVerifierTaskProducer>", "<target-ClaudeConfigManager-****************************************************************--entry>"], "outputs": ["/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app/Contents/Info.plist", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app/Contents/PkgInfo"]}, "P0:target-ClaudeConfigManager-****************************************************************-:Release:ProcessProductPackaging /Users/<USER>/XcodeProjects/ClaudeConfigManager/ClaudeConfigManager/ClaudeConfigManager.entitlements /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/ClaudeConfigManager.app.xcent": {"tool": "process-product-entitlements", "description": "ProcessProductPackaging /Users/<USER>/XcodeProjects/ClaudeConfigManager/ClaudeConfigManager/ClaudeConfigManager.entitlements /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/ClaudeConfigManager.app.xcent", "inputs": ["/Users/<USER>/XcodeProjects/ClaudeConfigManager/ClaudeConfigManager/ClaudeConfigManager.entitlements", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/DerivedSources/Entitlements.plist", "<target-ClaudeConfigManager-****************************************************************--ProductStructureTaskProducer>", "<target-ClaudeConfigManager-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/ClaudeConfigManager.app.xcent"]}, "P0:target-ClaudeConfigManager-****************************************************************-:Release:ProcessProductPackagingDER /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/ClaudeConfigManager.app.xcent /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/ClaudeConfigManager.app.xcent.der": {"tool": "shell", "description": "ProcessProductPackagingDER /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/ClaudeConfigManager.app.xcent /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/ClaudeConfigManager.app.xcent.der", "inputs": ["/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/ClaudeConfigManager.app.xcent", "<target-ClaudeConfigManager-****************************************************************--ProductStructureTaskProducer>", "<target-ClaudeConfigManager-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/ClaudeConfigManager.app.xcent.der"], "args": ["/usr/bin/derq", "query", "-f", "xml", "-i", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/ClaudeConfigManager.app.xcent", "-o", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/ClaudeConfigManager.app.xcent.der", "--raw"], "env": {}, "working-directory": "/Users/<USER>/XcodeProjects/ClaudeConfigManager", "signature": "4df5ed9609340851f1ae862c0bf316ce"}, "P0:target-ClaudeConfigManager-****************************************************************-:Release:RegisterExecutionPolicyException /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app": {"tool": "register-execution-policy-exception", "description": "RegisterExecutionPolicyException /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app", "inputs": ["/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app", "<target-ClaudeConfigManager-****************************************************************--Barrier-CodeSign>", "<target-ClaudeConfigManager-****************************************************************--will-sign>", "<target-ClaudeConfigManager-****************************************************************--entry>"], "outputs": ["<RegisterExecutionPolicyException /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app>"]}, "P0:target-ClaudeConfigManager-****************************************************************-:Release:RegisterWithLaunchServices /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app": {"tool": "lsregisterurl", "description": "RegisterWithLaunchServices /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app", "inputs": ["<target-ClaudeConfigManager-****************************************************************--Barrier-Validate>", "<target-ClaudeConfigManager-****************************************************************--will-sign>", "<target-ClaudeConfigManager-****************************************************************--entry>", "<TRIGGER: Validate /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app>"], "outputs": ["<LSRegisterURL /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app>"]}, "P0:target-ClaudeConfigManager-****************************************************************-:Release:SetMode u+w,go-w,a+rX /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app": {"tool": "shell", "description": "SetMode u+w,go-w,a+rX /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app", "inputs": ["<SetOwner /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app>", "<target-ClaudeConfigManager-****************************************************************--Barrier-StripSymbols>", "<target-ClaudeConfigManager-****************************************************************--will-sign>", "<target-ClaudeConfigManager-****************************************************************--entry>", "<TRIGGER: SetOwnerAndGroup xiaozhaodong:staff /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app>"], "outputs": ["<SetMode /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app>", "<TRIGGER: SetMode u+w,go-w,a+rX /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app>"], "args": ["/bin/chmod", "-RH", "u+w,go-w,a+rX", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app"], "env": {}, "working-directory": "/Users/<USER>/XcodeProjects/ClaudeConfigManager", "signature": "70b4792b88da1d0743f07b4514dcb81c"}, "P0:target-ClaudeConfigManager-****************************************************************-:Release:SetOwnerAndGroup xiaozhaodong:staff /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app": {"tool": "shell", "description": "SetOwnerAndGroup xiaozhaodong:staff /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app", "inputs": ["<target-ClaudeConfigManager-****************************************************************--Barrier-StripSymbols>", "<target-ClaudeConfigManager-****************************************************************--will-sign>", "<target-ClaudeConfigManager-****************************************************************--entry>", "<TRIGGER: MkDir /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app>"], "outputs": ["<SetOwner /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app>", "<TRIGGER: SetOwnerAndGroup xiaozhaodong:staff /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app>"], "args": ["/usr/sbin/chown", "-RH", "<PERSON>iaozhaodong:staff", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app"], "env": {}, "working-directory": "/Users/<USER>/XcodeProjects/ClaudeConfigManager", "signature": "7a783227bd71d6f490562db5c65b717e"}, "P0:target-ClaudeConfigManager-****************************************************************-:Release:Strip /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app/Contents/MacOS/ClaudeConfigManager": {"tool": "shell", "description": "Strip /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app/Contents/MacOS/ClaudeConfigManager", "inputs": ["<GenerateDSYMFile /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/BuildProductsPath/Release/ClaudeConfigManager.app.dSYM/Contents/Resources/DWARF/ClaudeConfigManager>", "<target-ClaudeConfigManager-****************************************************************--Barrier-CopyAside>", "<target-ClaudeConfigManager-****************************************************************--will-sign>", "<target-ClaudeConfigManager-****************************************************************--entry>", "<TRIGGER: Ld /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app/Contents/MacOS/ClaudeConfigManager normal>"], "outputs": ["<Strip /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app/Contents/MacOS/ClaudeConfigManager>", "<TRIGGER: Strip /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app/Contents/MacOS/ClaudeConfigManager>"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/strip", "-D", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app/Contents/MacOS/ClaudeConfigManager"], "env": {}, "working-directory": "/Users/<USER>/XcodeProjects/ClaudeConfigManager", "signature": "913a1f371c7b3cdadfe1ef296c15847a"}, "P0:target-ClaudeConfigManager-****************************************************************-:Release:SwiftDriver Compilation ClaudeConfigManager normal x86_64 com.apple.xcode.tools.swift.compiler": {"tool": "swift-driver-compilation", "description": "SwiftDriver Compilation ClaudeConfigManager normal x86_64 com.apple.xcode.tools.swift.compiler", "inputs": ["/Users/<USER>/XcodeProjects/ClaudeConfigManager/ClaudeConfigManager/Features/ContentView.swift", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/ClaudeConfigManager/Core/Services/ConfigService.swift", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/ClaudeConfigManager/Features/MenuBar/StatusItemManager.swift", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/ClaudeConfigManager/Core/Models/ClaudeConfig.swift", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/ClaudeConfigManager/App/AppState.swift", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/ClaudeConfigManager/Features/MenuBar/MenuBarView.swift", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/ClaudeConfigManager/App/ClaudeConfigManagerApp.swift", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/ClaudeConfigManager/App/AppDelegate.swift", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/ClaudeConfigManager/Features/MenuBar/MenuBarViewModel.swift", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/ClaudeConfigManager/Core/Services/KeychainService.swift", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/ClaudeConfigManager/Core/Services/ProcessService.swift", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/ClaudeConfigManager/Core/Services/Logger.swift", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager.SwiftFileList", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager-OutputFileMap.json", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager_const_extract_protocols.json", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/ClaudeConfigManager-generated-files.hmap", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/ClaudeConfigManager-own-target-headers.hmap", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/ClaudeConfigManager-all-target-headers.hmap", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/ClaudeConfigManager-project-headers.hmap", "<ClangStatCache /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/SDKStatCaches.noindex/macosx15.5-24F74-8254517eb8b97d462ccbc072ba9094c9.sdkstatcache>", "<target-ClaudeConfigManager-****************************************************************--copy-headers-completion>", "<target-ClaudeConfigManager-****************************************************************--ModuleVerifierTaskProducer>", "<target-ClaudeConfigManager-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager Swift Compilation Finished"]}, "P0:target-ClaudeConfigManager-****************************************************************-:Release:SymLink /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/BuildProductsPath/Release/ClaudeConfigManager.app ../../InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app": {"tool": "symlink", "description": "SymLink /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/BuildProductsPath/Release/ClaudeConfigManager.app ../../InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app", "inputs": ["<target-ClaudeConfigManager-****************************************************************--start>", "<target-ClaudeConfigManager-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/BuildProductsPath/Release/ClaudeConfigManager.app"], "contents": "../../InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app", "repair-via-ownership-analysis": true}, "P0:target-ClaudeConfigManager-****************************************************************-:Release:Touch /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app": {"tool": "shell", "description": "Touch /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app", "inputs": ["/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app", "<target-ClaudeConfigManager-****************************************************************--Barrier-Validate>", "<target-ClaudeConfigManager-****************************************************************--will-sign>", "<target-ClaudeConfigManager-****************************************************************--entry>"], "outputs": ["<Touch /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app>"], "args": ["/usr/bin/touch", "-c", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app"], "env": {}, "working-directory": "/Users/<USER>/XcodeProjects/ClaudeConfigManager", "signature": "8112fea62577fe5724709185d6e75c70"}, "P0:target-ClaudeConfigManager-****************************************************************-:Release:Validate /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app": {"tool": "validate-product", "description": "Validate /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app", "inputs": ["/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app/Contents/Info.plist", "<target-ClaudeConfigManager-****************************************************************--<PERSON>ier-RegisterExecutionPolicyException>", "<target-ClaudeConfigManager-****************************************************************--will-sign>", "<target-ClaudeConfigManager-****************************************************************--entry>", "<TRIGGER: CodeSign /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app>"], "outputs": ["<Validate /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app>", "<TRIGGER: Validate /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app>"]}, "P0:target-ClaudeConfigManager-****************************************************************-:Release:ValidateDevelopmentAssets /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build": {"tool": "validate-development-assets", "description": "ValidateDevelopmentAssets /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build", "inputs": ["/Users/<USER>/XcodeProjects/ClaudeConfigManager/ClaudeConfigManager/Preview Content", "<target-ClaudeConfigManager-****************************************************************--entry>"], "outputs": ["<ValidateDevelopmentAssets-/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build>"], "allow-missing-inputs": true}, "P2:::WriteAuxiliaryFile /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager-dd5074e12287a3259fdfd8e81b104e7a-VFS/all-product-headers.yaml": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager-dd5074e12287a3259fdfd8e81b104e7a-VFS/all-product-headers.yaml", "inputs": ["/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath"], "outputs": ["/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager-dd5074e12287a3259fdfd8e81b104e7a-VFS/all-product-headers.yaml"]}, "P2:target-ClaudeConfigManager-****************************************************************-:Release:Copy /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/BuildProductsPath/Release/ClaudeConfigManager.swiftmodule/x86_64-apple-macos.abi.json /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager.abi.json": {"tool": "file-copy", "description": "Copy /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/BuildProductsPath/Release/ClaudeConfigManager.swiftmodule/x86_64-apple-macos.abi.json /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager.abi.json", "inputs": ["/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager.abi.json/", "<target-ClaudeConfigManager-****************************************************************--copy-headers-completion>", "<target-ClaudeConfigManager-****************************************************************--ModuleVerifierTaskProducer>", "<target-ClaudeConfigManager-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/BuildProductsPath/Release/ClaudeConfigManager.swiftmodule/x86_64-apple-macos.abi.json"]}, "P2:target-ClaudeConfigManager-****************************************************************-:Release:Copy /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/BuildProductsPath/Release/ClaudeConfigManager.swiftmodule/x86_64-apple-macos.swiftdoc /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager.swiftdoc": {"tool": "file-copy", "description": "Copy /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/BuildProductsPath/Release/ClaudeConfigManager.swiftmodule/x86_64-apple-macos.swiftdoc /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager.swiftdoc", "inputs": ["/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager.swiftdoc/", "<target-ClaudeConfigManager-****************************************************************--copy-headers-completion>", "<target-ClaudeConfigManager-****************************************************************--ModuleVerifierTaskProducer>", "<target-ClaudeConfigManager-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/BuildProductsPath/Release/ClaudeConfigManager.swiftmodule/x86_64-apple-macos.swiftdoc"]}, "P2:target-ClaudeConfigManager-****************************************************************-:Release:Copy /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/BuildProductsPath/Release/ClaudeConfigManager.swiftmodule/x86_64-apple-macos.swiftmodule /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager.swiftmodule": {"tool": "file-copy", "description": "Copy /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/BuildProductsPath/Release/ClaudeConfigManager.swiftmodule/x86_64-apple-macos.swiftmodule /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager.swiftmodule", "inputs": ["/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager.swiftmodule/", "<target-ClaudeConfigManager-****************************************************************--copy-headers-completion>", "<target-ClaudeConfigManager-****************************************************************--ModuleVerifierTaskProducer>", "<target-ClaudeConfigManager-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/BuildProductsPath/Release/ClaudeConfigManager.swiftmodule/x86_64-apple-macos.swiftmodule"]}, "P2:target-ClaudeConfigManager-****************************************************************-:Release:Ld /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app/Contents/MacOS/ClaudeConfigManager normal": {"tool": "shell", "description": "Ld /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app/Contents/MacOS/ClaudeConfigManager normal", "inputs": ["/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ContentView.o", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ConfigService.o", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/StatusItemManager.o", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfig.o", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/AppState.o", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/MenuBarView.o", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManagerApp.o", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/AppDelegate.o", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/MenuBarViewModel.o", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/KeychainService.o", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ProcessService.o", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/Logger.o", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/GeneratedAssetSymbols.o", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager.LinkFileList", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/BuildProductsPath/Release", "<target-ClaudeConfigManager-****************************************************************--generated-headers>", "<target-ClaudeConfigManager-****************************************************************--swift-generated-headers>", "<target-ClaudeConfigManager-****************************************************************--ModuleVerifierTaskProducer>", "<target-ClaudeConfigManager-****************************************************************--begin-linking>"], "outputs": ["/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app/Contents/MacOS/ClaudeConfigManager", "<Linked Binary /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app/Contents/MacOS/ClaudeConfigManager>", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager_lto.o", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager_dependency_info.dat", "<TRIGGER: Ld /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app/Contents/MacOS/ClaudeConfigManager normal>"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang", "-<PERSON><PERSON><PERSON>", "-reproducible", "-target", "x86_64-apple-macos15.0", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "-<PERSON><PERSON>", "-L/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/EagerLinkingTBDs/Release", "-L/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/BuildProductsPath/Release", "-F/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/EagerLinkingTBDs/Release", "-F/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/BuildProductsPath/Release", "-filelist", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager.LinkFileList", "-<PERSON><PERSON><PERSON>", "-rpath", "-<PERSON><PERSON><PERSON>", "@executable_path/../Frameworks", "-<PERSON><PERSON><PERSON>", "-object_path_lto", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager_lto.o", "-<PERSON><PERSON><PERSON>", "-final_output", "-<PERSON><PERSON><PERSON>", "/Applications/ClaudeConfigManager.app/Contents/MacOS/ClaudeConfigManager", "-<PERSON><PERSON><PERSON>", "-dependency_info", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager_dependency_info.dat", "-fobjc-link-runtime", "-L/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx", "-L/usr/lib/swift", "-<PERSON><PERSON><PERSON>", "-add_ast_path", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager.swiftmodule", "-<PERSON><PERSON><PERSON>", "-no_adhoc_codesign", "-o", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/InstallationBuildProductsLocation/Applications/ClaudeConfigManager.app/Contents/MacOS/ClaudeConfigManager"], "env": {}, "working-directory": "/Users/<USER>/XcodeProjects/ClaudeConfigManager", "deps": ["/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager_dependency_info.dat"], "deps-style": "dependency-info", "signature": "553b396fb65f00eb9491f428d9c6e339"}, "P2:target-ClaudeConfigManager-****************************************************************-:Release:SwiftDriver Compilation Requirements ClaudeConfigManager normal x86_64 com.apple.xcode.tools.swift.compiler": {"tool": "swift-driver-compilation-requirement", "description": "SwiftDriver Compilation Requirements ClaudeConfigManager normal x86_64 com.apple.xcode.tools.swift.compiler", "inputs": ["/Users/<USER>/XcodeProjects/ClaudeConfigManager/ClaudeConfigManager/Features/ContentView.swift", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/ClaudeConfigManager/Core/Services/ConfigService.swift", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/ClaudeConfigManager/Features/MenuBar/StatusItemManager.swift", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/ClaudeConfigManager/Core/Models/ClaudeConfig.swift", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/ClaudeConfigManager/App/AppState.swift", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/ClaudeConfigManager/Features/MenuBar/MenuBarView.swift", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/ClaudeConfigManager/App/ClaudeConfigManagerApp.swift", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/ClaudeConfigManager/App/AppDelegate.swift", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/ClaudeConfigManager/Features/MenuBar/MenuBarViewModel.swift", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/ClaudeConfigManager/Core/Services/KeychainService.swift", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/ClaudeConfigManager/Core/Services/ProcessService.swift", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/ClaudeConfigManager/Core/Services/Logger.swift", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager.SwiftFileList", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager-OutputFileMap.json", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager_const_extract_protocols.json", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/ClaudeConfigManager-generated-files.hmap", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/ClaudeConfigManager-own-target-headers.hmap", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/ClaudeConfigManager-all-target-headers.hmap", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/ClaudeConfigManager-project-headers.hmap", "<ClangStatCache /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/SDKStatCaches.noindex/macosx15.5-24F74-8254517eb8b97d462ccbc072ba9094c9.sdkstatcache>", "<target-ClaudeConfigManager-****************************************************************--copy-headers-completion>", "<target-ClaudeConfigManager-****************************************************************--ModuleVerifierTaskProducer>", "<target-ClaudeConfigManager-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ContentView.o", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ConfigService.o", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/StatusItemManager.o", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfig.o", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/AppState.o", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/MenuBarView.o", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManagerApp.o", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/AppDelegate.o", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/MenuBarViewModel.o", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/KeychainService.o", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ProcessService.o", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/Logger.o", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/GeneratedAssetSymbols.o", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ContentView.stringsdata", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ConfigService.stringsdata", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/StatusItemManager.stringsdata", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfig.stringsdata", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/AppState.stringsdata", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/MenuBarView.stringsdata", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManagerApp.stringsdata", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/AppDelegate.stringsdata", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/MenuBarViewModel.stringsdata", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/KeychainService.stringsdata", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ProcessService.stringsdata", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/Logger.stringsdata", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager-master.swiftconstvalues", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager.swiftmodule", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager.swiftsourceinfo", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager.abi.json", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager-Swift.h", "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager.swiftdoc"]}, "P2:target-ClaudeConfigManager-****************************************************************-:Release:SwiftMergeGeneratedHeaders /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/DerivedSources/ClaudeConfigManager-Swift.h /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager-Swift.h": {"tool": "swift-header-tool", "description": "SwiftMergeGeneratedHeaders /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/DerivedSources/ClaudeConfigManager-Swift.h /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager-Swift.h", "inputs": ["/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager-Swift.h", "<target-ClaudeConfigManager-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/DerivedSources/ClaudeConfigManager-Swift.h"]}, "P2:target-ClaudeConfigManager-****************************************************************-:Release:WriteAuxiliaryFile /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/ClaudeConfigManager-all-non-framework-target-headers.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/ClaudeConfigManager-all-non-framework-target-headers.hmap", "inputs": ["<target-ClaudeConfigManager-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/ClaudeConfigManager-all-non-framework-target-headers.hmap"]}, "P2:target-ClaudeConfigManager-****************************************************************-:Release:WriteAuxiliaryFile /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/ClaudeConfigManager-all-target-headers.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/ClaudeConfigManager-all-target-headers.hmap", "inputs": ["<target-ClaudeConfigManager-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/ClaudeConfigManager-all-target-headers.hmap"]}, "P2:target-ClaudeConfigManager-****************************************************************-:Release:WriteAuxiliaryFile /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/ClaudeConfigManager-generated-files.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/ClaudeConfigManager-generated-files.hmap", "inputs": ["<target-ClaudeConfigManager-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/ClaudeConfigManager-generated-files.hmap"]}, "P2:target-ClaudeConfigManager-****************************************************************-:Release:WriteAuxiliaryFile /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/ClaudeConfigManager-own-target-headers.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/ClaudeConfigManager-own-target-headers.hmap", "inputs": ["<target-ClaudeConfigManager-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/ClaudeConfigManager-own-target-headers.hmap"]}, "P2:target-ClaudeConfigManager-****************************************************************-:Release:WriteAuxiliaryFile /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/ClaudeConfigManager-project-headers.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/ClaudeConfigManager-project-headers.hmap", "inputs": ["<target-ClaudeConfigManager-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/ClaudeConfigManager-project-headers.hmap"]}, "P2:target-ClaudeConfigManager-****************************************************************-:Release:WriteAuxiliaryFile /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/ClaudeConfigManager.DependencyMetadataFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/ClaudeConfigManager.DependencyMetadataFileList", "inputs": ["<target-ClaudeConfigManager-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/ClaudeConfigManager.DependencyMetadataFileList"]}, "P2:target-ClaudeConfigManager-****************************************************************-:Release:WriteAuxiliaryFile /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/ClaudeConfigManager.DependencyStaticMetadataFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/ClaudeConfigManager.DependencyStaticMetadataFileList", "inputs": ["<target-ClaudeConfigManager-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/ClaudeConfigManager.DependencyStaticMetadataFileList"]}, "P2:target-ClaudeConfigManager-****************************************************************-:Release:WriteAuxiliaryFile /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/ClaudeConfigManager.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/ClaudeConfigManager.hmap", "inputs": ["<target-ClaudeConfigManager-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/ClaudeConfigManager.hmap"]}, "P2:target-ClaudeConfigManager-****************************************************************-:Release:WriteAuxiliaryFile /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/DerivedSources/Entitlements.plist": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/DerivedSources/Entitlements.plist", "inputs": ["<target-ClaudeConfigManager-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/DerivedSources/Entitlements.plist"]}, "P2:target-ClaudeConfigManager-****************************************************************-:Release:WriteAuxiliaryFile /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager-OutputFileMap.json": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager-OutputFileMap.json", "inputs": ["<target-ClaudeConfigManager-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager-OutputFileMap.json"]}, "P2:target-ClaudeConfigManager-****************************************************************-:Release:WriteAuxiliaryFile /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager.LinkFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager.LinkFileList", "inputs": ["<target-ClaudeConfigManager-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager.LinkFileList"]}, "P2:target-ClaudeConfigManager-****************************************************************-:Release:WriteAuxiliaryFile /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager.SwiftConstValuesFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager.SwiftConstValuesFileList", "inputs": ["<target-ClaudeConfigManager-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager.SwiftConstValuesFileList"]}, "P2:target-ClaudeConfigManager-****************************************************************-:Release:WriteAuxiliaryFile /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager.SwiftFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager.SwiftFileList", "inputs": ["<target-ClaudeConfigManager-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager.SwiftFileList"]}, "P2:target-ClaudeConfigManager-****************************************************************-:Release:WriteAuxiliaryFile /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager_const_extract_protocols.json": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager_const_extract_protocols.json", "inputs": ["<target-ClaudeConfigManager-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager_const_extract_protocols.json"]}, "P2:target-ClaudeConfigManager-****************************************************************-:Release:WriteAuxiliaryFile /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/empty-ClaudeConfigManager.plist": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/empty-ClaudeConfigManager.plist", "inputs": ["<target-ClaudeConfigManager-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/empty-ClaudeConfigManager.plist"]}}}