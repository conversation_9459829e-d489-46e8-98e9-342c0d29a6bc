{"source": "/Users/<USER>/XcodeProjects/ClaudeConfigManager/ClaudeConfigManager/Features/MenuBar/MenuBarView.swift", "tables": {"Localizable": [{"comment": "", "key": "Claude 配置管理器", "location": {"startingColumn": 26, "startingLine": 67}}, {"comment": "", "key": "当前配置", "location": {"startingColumn": 38, "startingLine": 196}}, {"comment": "", "key": "搜索配置...", "location": {"startingColumn": 23, "startingLine": 396}}, {"comment": "", "key": "可用配置", "location": {"startingColumn": 22, "startingLine": 444}}, {"comment": "", "key": "(%lld)", "location": {"startingColumn": 26, "startingLine": 449}}, {"comment": "", "key": "选择配置目录", "location": {"startingColumn": 20, "startingLine": 593}}, {"comment": "", "key": "授权 ~/.claude 目录", "location": {"startingColumn": 20, "startingLine": 600}}, {"comment": "", "key": "未找到配置文件", "location": {"startingColumn": 22, "startingLine": 583}}, {"comment": "", "key": "请选择包含配置文件的目录", "location": {"startingColumn": 22, "startingLine": 587}}, {"comment": "", "key": "当前", "location": {"startingColumn": 34, "startingLine": 679}}, {"comment": "", "key": "<PERSON> 进程", "location": {"startingColumn": 22, "startingLine": 818}}, {"comment": "", "key": "退出 Claude 配置管理器", "location": {"startingColumn": 24, "startingLine": 1118}}, {"comment": "", "key": "版本 1.0.0", "location": {"startingColumn": 26, "startingLine": 1095}}, {"comment": "", "key": "⌘Q", "location": {"startingColumn": 30, "startingLine": 1103}}, {"comment": "", "key": "退出", "location": {"startingColumn": 30, "startingLine": 1111}}, {"comment": "", "key": "PID: %d", "location": {"startingColumn": 30, "startingLine": 1183}}, {"comment": "", "key": "CPU: %@", "location": {"startingColumn": 34, "startingLine": 1195}}, {"comment": "", "key": "内存: %@", "location": {"startingColumn": 34, "startingLine": 1205}}, {"comment": "", "key": "没有检测到 Claude 进程", "location": {"startingColumn": 22, "startingLine": 1368}}, {"comment": "", "key": "Claude CLI 当前未在运行", "location": {"startingColumn": 22, "startingLine": 1372}}]}, "version": 1}