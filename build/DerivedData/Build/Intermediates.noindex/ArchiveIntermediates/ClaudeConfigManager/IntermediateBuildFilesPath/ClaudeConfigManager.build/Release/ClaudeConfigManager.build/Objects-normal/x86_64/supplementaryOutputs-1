"/Users/<USER>/XcodeProjects/ClaudeConfigManager/ClaudeConfigManager/Features/ContentView.swift":
  swiftsourceinfo: "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager.swiftsourceinfo"
  swiftmodule: "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager.swiftmodule"
  const-values: "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager-master.swiftconstvalues"
  abi-baseline-json: "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager.abi.json"
  dependencies: "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager-master.d"
  diagnostics: "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager-master.dia"
  objc-header: "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager-Swift.h"
  swiftdoc: "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager.swiftdoc"
