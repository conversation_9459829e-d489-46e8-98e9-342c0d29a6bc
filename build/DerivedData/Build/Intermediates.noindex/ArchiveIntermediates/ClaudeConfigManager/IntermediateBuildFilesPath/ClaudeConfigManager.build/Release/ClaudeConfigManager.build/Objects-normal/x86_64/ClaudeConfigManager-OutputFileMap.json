{"": {"const-values": "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager-master.swiftconstvalues", "dependencies": "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager-master.d", "diagnostics": "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager-master.dia", "emit-module-dependencies": "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager-master-emit-module.d", "emit-module-diagnostics": "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager-master-emit-module.dia", "swift-dependencies": "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManager-master.swiftdeps"}, "/Users/<USER>/XcodeProjects/ClaudeConfigManager/ClaudeConfigManager/App/AppDelegate.swift": {"index-unit-output-path": "/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/AppDelegate.o", "llvm-bc": "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/AppDelegate.bc", "object": "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/AppDelegate.o"}, "/Users/<USER>/XcodeProjects/ClaudeConfigManager/ClaudeConfigManager/App/AppState.swift": {"index-unit-output-path": "/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/AppState.o", "llvm-bc": "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/AppState.bc", "object": "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/AppState.o"}, "/Users/<USER>/XcodeProjects/ClaudeConfigManager/ClaudeConfigManager/App/ClaudeConfigManagerApp.swift": {"index-unit-output-path": "/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManagerApp.o", "llvm-bc": "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManagerApp.bc", "object": "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfigManagerApp.o"}, "/Users/<USER>/XcodeProjects/ClaudeConfigManager/ClaudeConfigManager/Core/Models/ClaudeConfig.swift": {"index-unit-output-path": "/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfig.o", "llvm-bc": "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfig.bc", "object": "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ClaudeConfig.o"}, "/Users/<USER>/XcodeProjects/ClaudeConfigManager/ClaudeConfigManager/Core/Services/ConfigService.swift": {"index-unit-output-path": "/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ConfigService.o", "llvm-bc": "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ConfigService.bc", "object": "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ConfigService.o"}, "/Users/<USER>/XcodeProjects/ClaudeConfigManager/ClaudeConfigManager/Core/Services/KeychainService.swift": {"index-unit-output-path": "/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/KeychainService.o", "llvm-bc": "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/KeychainService.bc", "object": "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/KeychainService.o"}, "/Users/<USER>/XcodeProjects/ClaudeConfigManager/ClaudeConfigManager/Core/Services/Logger.swift": {"index-unit-output-path": "/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/Logger.o", "llvm-bc": "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/Logger.bc", "object": "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/Logger.o"}, "/Users/<USER>/XcodeProjects/ClaudeConfigManager/ClaudeConfigManager/Core/Services/ProcessService.swift": {"index-unit-output-path": "/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ProcessService.o", "llvm-bc": "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ProcessService.bc", "object": "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ProcessService.o"}, "/Users/<USER>/XcodeProjects/ClaudeConfigManager/ClaudeConfigManager/Features/ContentView.swift": {"index-unit-output-path": "/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ContentView.o", "llvm-bc": "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ContentView.bc", "object": "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/ContentView.o"}, "/Users/<USER>/XcodeProjects/ClaudeConfigManager/ClaudeConfigManager/Features/MenuBar/MenuBarView.swift": {"index-unit-output-path": "/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/MenuBarView.o", "llvm-bc": "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/MenuBarView.bc", "object": "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/MenuBarView.o"}, "/Users/<USER>/XcodeProjects/ClaudeConfigManager/ClaudeConfigManager/Features/MenuBar/MenuBarViewModel.swift": {"index-unit-output-path": "/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/MenuBarViewModel.o", "llvm-bc": "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/MenuBarViewModel.bc", "object": "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/MenuBarViewModel.o"}, "/Users/<USER>/XcodeProjects/ClaudeConfigManager/ClaudeConfigManager/Features/MenuBar/StatusItemManager.swift": {"index-unit-output-path": "/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/StatusItemManager.o", "llvm-bc": "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/StatusItemManager.bc", "object": "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/StatusItemManager.o"}, "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/DerivedSources/GeneratedAssetSymbols.swift": {"index-unit-output-path": "/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/GeneratedAssetSymbols.o", "llvm-bc": "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/GeneratedAssetSymbols.bc", "object": "/Users/<USER>/XcodeProjects/ClaudeConfigManager/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/ClaudeConfigManager/IntermediateBuildFilesPath/ClaudeConfigManager.build/Release/ClaudeConfigManager.build/Objects-normal/x86_64/GeneratedAssetSymbols.o"}}