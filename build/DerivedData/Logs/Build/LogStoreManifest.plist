<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>logFormatVersion</key>
	<integer>11</integer>
	<key>logs</key>
	<dict>
		<key>93D7B6A1-48A2-4A6D-ACA6-BD7835EC52DB</key>
		<dict>
			<key>className</key>
			<string>IDECommandLineBuildLog</string>
			<key>documentTypeString</key>
			<string>&lt;nil&gt;</string>
			<key>domainType</key>
			<string>Xcode.IDEActivityLogDomainType.BuildLog</string>
			<key>fileName</key>
			<string>93D7B6A1-48A2-4A6D-ACA6-BD7835EC52DB.xcactivitylog</string>
			<key>hasPrimaryLog</key>
			<true/>
			<key>primaryObservable</key>
			<dict>
				<key>highLevelStatus</key>
				<string>W</string>
				<key>totalNumberOfAnalyzerIssues</key>
				<integer>0</integer>
				<key>totalNumberOfErrors</key>
				<integer>0</integer>
				<key>totalNumberOfTestFailures</key>
				<integer>0</integer>
				<key>totalNumberOfWarnings</key>
				<integer>5</integer>
			</dict>
			<key>schemeIdentifier-containerName</key>
			<string>ClaudeConfigManager project</string>
			<key>schemeIdentifier-schemeName</key>
			<string>ClaudeConfigManager</string>
			<key>schemeIdentifier-sharedScheme</key>
			<integer>1</integer>
			<key>signature</key>
			<string>Archiving project ClaudeConfigManager with scheme ClaudeConfigManager</string>
			<key>timeStartedRecording</key>
			<real>775450140.362854</real>
			<key>timeStoppedRecording</key>
			<real>775450165.40509295</real>
			<key>title</key>
			<string>Archiving project ClaudeConfigManager with scheme ClaudeConfigManager</string>
			<key>uniqueIdentifier</key>
			<string>93D7B6A1-48A2-4A6D-ACA6-BD7835EC52DB</string>
		</dict>
	</dict>
</dict>
</plist>
