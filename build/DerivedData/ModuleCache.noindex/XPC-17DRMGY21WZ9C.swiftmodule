---
path:            '/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/XPC.swiftmodule/x86_64-apple-macos.swiftmodule'
dependencies:
  - mtime:           1746401359000000000
    path:            '/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/XPC.swiftmodule/x86_64-apple-macos.swiftmodule'
    size:            96964
  - mtime:           1745034367000000000
    path:            'usr/lib/swift/Swift.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            1912950
    sdk_relative:    true
  - mtime:           1745035158000000000
    path:            'usr/include/_time.apinotes'
    size:            1132
    sdk_relative:    true
  - mtime:           1745043435000000000
    path:            'usr/include/ObjectiveC.apinotes'
    size:            11147
    sdk_relative:    true
  - mtime:           1745030861000000000
    path:            'usr/include/Dispatch.apinotes'
    size:            19
    sdk_relative:    true
  - mtime:           1745376776000000000
    path:            'usr/include/XPC.apinotes'
    size:            123
    sdk_relative:    true
  - mtime:           1745035390000000000
    path:            'usr/lib/swift/_errno.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            3890
    sdk_relative:    true
  - mtime:           1745035397000000000
    path:            'usr/lib/swift/_time.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            1065
    sdk_relative:    true
  - mtime:           1745035419000000000
    path:            'usr/lib/swift/_signal.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            1101
    sdk_relative:    true
  - mtime:           1745035419000000000
    path:            'usr/lib/swift/sys_time.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            1102
    sdk_relative:    true
  - mtime:           1745035413000000000
    path:            'usr/lib/swift/_stdio.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            1513
    sdk_relative:    true
  - mtime:           1745035426000000000
    path:            'usr/lib/swift/unistd.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            853
    sdk_relative:    true
  - mtime:           1745035390000000000
    path:            'usr/lib/swift/_math.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            22628
    sdk_relative:    true
  - mtime:           1745034431000000000
    path:            'usr/lib/swift/_Builtin_float.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            5775
    sdk_relative:    true
  - mtime:           1745035444000000000
    path:            'usr/lib/swift/Darwin.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            18255
    sdk_relative:    true
  - mtime:           1745035821000000000
    path:            'usr/lib/swift/_Concurrency.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            230631
    sdk_relative:    true
  - mtime:           1745036006000000000
    path:            'usr/lib/swift/_StringProcessing.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            22908
    sdk_relative:    true
  - mtime:           1745036535000000000
    path:            'System/Library/Frameworks/Combine.framework/Modules/Combine.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            167834
    sdk_relative:    true
  - mtime:           1745036485000000000
    path:            'usr/lib/swift/ObjectiveC.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            6610
    sdk_relative:    true
  - mtime:           1745036728000000000
    path:            'usr/lib/swift/Dispatch.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            57170
    sdk_relative:    true
  - mtime:           1745037658000000000
    path:            'usr/lib/swift/XPC.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            33654
    sdk_relative:    true
version:         1
...
